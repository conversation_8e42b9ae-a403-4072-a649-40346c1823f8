"use client";

import React, { useState } from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { UserCircle, Bell, Save, Languages, ArrowRight, Home, Database, TestTube, Eye, AlertCircle } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON>ertDes<PERSON>, AlertTitle } from "@/components/ui/alert";
import { useAuth } from "@/contexts/auth-context";

export default function SettingsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAuth();

  const saveSettings = (section: string) => {
    toast({ title: "تم الحفظ", description: `تم حفظ إعدادات قسم ${section} (تجريبي).` });
  };
  
  const handleTestConnection = async () => {
    if (!currentUser) {
      toast({
        variant: 'destructive',
        title: '❌ فشل الفحص',
        description: 'يجب أن تكون مسجلاً للدخول لتتمكن من فحص الاتصال.',
      });
      return;
    }
    
    toast({
        title: 'جاري فحص الاتصال...',
        description: 'نحاول الآن الاتصال بقاعدة البيانات...',
    });

    try {
      // Simulate database connection test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: '✅ نجاح الاتصال',
        description: 'تم الاتصال بقاعدة البيانات وقراءة البيانات بنجاح.',
      });
    } catch (error: any) {
      console.error('Database connection test error:', error);
      toast({
        variant: 'destructive',
        title: '❌ فشل الاتصال',
        description: `لا يمكن الاتصال بقاعدة البيانات. تحقق من إعدادات قاعدة البيانات. الخطأ: ${error.message}`,
        duration: 9000,
      });
    }
  };

  return (
    <>
      <PageHeader
        title="الإعدادات"
        description="إدارة إعدادات ملفك الشخصي وتفضيلات التطبيق."
      >
        <div className="flex flex-wrap gap-2 items-center">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowRight className="h-4 w-4" />
            رجوع
          </Button>
          <Button asChild variant="outline">
            <Link href="/">
              <Home className="h-4 w-4" />
              الرئيسية
            </Link>
          </Button>
        </div>
      </PageHeader>
      
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-6">
            <TabsTrigger value="profile" className="flex items-center gap-2"><UserCircle className="h-4 w-4" /> الملف الشخصي</TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2"><Bell className="h-4 w-4" /> الإشعارات</TabsTrigger>
            <TabsTrigger value="database" className="flex items-center gap-2"><Database className="h-4 w-4" /> قاعدة البيانات</TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <Card className="shadow-lg border-2 border-primary">
              <CardHeader>
                <CardTitle>إعدادات الملف الشخصي</CardTitle>
                <CardDescription>تحديث معلوماتك الشخصية وتفاصيل الاتصال.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col items-center space-y-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src="https://placehold.co/100x100.png" alt="User Avatar" />
                    <AvatarFallback>{currentUser?.name?.charAt(0) || 'U'}</AvatarFallback>
                  </Avatar>
                  <Button variant="outline" onClick={() => toast({ title: 'قيد التطوير', description: 'سيتم تفعيل تغيير الصورة قريباً'})}>تغيير الصورة الرمزية</Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <Label htmlFor="fullName">الاسم الكامل</Label>
                    <Input id="fullName" defaultValue={currentUser?.name || ''} />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input id="email" type="email" defaultValue={currentUser?.email || ''} />
                  </div>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="phoneNumber">رقم الهاتف</Label>
                  <Input id="phoneNumber" type="tel" placeholder="07XXXXXXXXX" />
                </div>
                 <div className="space-y-2 p-3 rounded-md border">
                  <Label htmlFor="appLanguage" className="font-medium flex items-center gap-1"><Languages className="h-4 w-4"/> لغة الواجهة</Label>
                   <p className="text-sm text-muted-foreground">اختر لغة عرض واجهة التطبيق.</p>
                  <Select defaultValue="ar">
                      <SelectTrigger id="appLanguage"><SelectValue placeholder="اختر لغة"/></SelectTrigger>
                      <SelectContent>
                          <SelectItem value="ar">العربية (Arabic)</SelectItem>
                          <SelectItem value="en" disabled>الإنجليزية (English) - قريباً</SelectItem>
                      </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end">
                  <Button onClick={() => saveSettings("الملف الشخصي")}><Save className="ml-2 h-4 w-4"/> حفظ التغييرات</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card className="shadow-lg border-2 border-primary">
              <CardHeader>
                <CardTitle>إعدادات الإشعارات</CardTitle>
                <CardDescription>اختر كيف ومتى تريد تلقي الإشعارات.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between space-x-2 space-x-reverse p-3 rounded-md border">
                  <div>
                    <Label htmlFor="emailNotifications" className="font-medium">إشعارات البريد الإلكتروني</Label>
                    <p className="text-sm text-muted-foreground">تلقي ملخصات وتقارير هامة عبر البريد.</p>
                  </div>
                  <Switch id="emailNotifications" defaultChecked />
                </div>
                 <div className="flex items-center justify-between space-x-2 space-x-reverse p-3 rounded-md border">
                   <div>
                      <Label htmlFor="appNotifications" className="font-medium">إشعارات داخل التطبيق</Label>
                      <p className="text-sm text-muted-foreground">تنبيهات فورية عند تحديث الطلبات أو ورود تذاكر.</p>
                   </div>
                  <Switch id="appNotifications" defaultChecked />
                </div>
                 <div className="flex items-center justify-between space-x-2 space-x-reverse p-3 rounded-md border">
                   <div>
                      <Label htmlFor="smsNotifications" className="font-medium">إشعارات الرسائل القصيرة (SMS)</Label>
                      <p className="text-sm text-muted-foreground">تنبيهات حرجة على رقم هاتفك (قد تطبق رسوم).</p>
                   </div>
                  <Switch id="smsNotifications" />
                </div>
                <div className="flex justify-end">
                  <Button onClick={() => saveSettings("الإشعارات")}><Save className="ml-2 h-4 w-4"/> حفظ التفضيلات</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="database">
            <Card className="shadow-lg border-2 border-primary">
              <CardHeader>
                <CardTitle>إدارة قاعدة البيانات</CardTitle>
                <CardDescription>أدوات لفحص الاتصال وإدارة البيانات السحابية.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                 <Alert variant="default" className="border-primary">
                  <TestTube className="h-4 w-4" />
                  <AlertTitle className="font-bold">اختبار اتصال قاعدة البيانات</AlertTitle>
                  <AlertDescription>
                     هذا الاختبار سيقوم بمحاولة قراءة بياناتك من قاعدة البيانات السحابية للتحقق من أن الاتصال والمزامنة يعملان بشكل سليم.
                  </AlertDescription>
                  <Button onClick={handleTestConnection} className="mt-4">
                    <TestTube className="ml-2 h-4 w-4" />
                    إجراء الاختبار الآن
                  </Button>
                </Alert>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>عرض البيانات</AlertTitle>
                  <AlertDescription>
                    لعرض بياناتك الحالية، يرجى استخدام لوحة تحكم قاعدة البيانات التي توفر واجهة رسومية قوية لتصفح وتعديل البيانات مباشرة.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
