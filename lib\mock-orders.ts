import type { Order, OrderStatus, AccountingRecordRep } from "@/types";

// Mock orders for testing
export const mockOrders: Order[] = [
  {
    id: "order-001",
    orderNumber: "R001",
    companyName: "شركة النور للتجارة",
    mobileNumber: "***********",
    amount: 50000,
    status: "تم التسليم" as OrderStatus,
    agentId: "rep-001",
    createdAt: new Date("2024-01-15").toISOString(),
    updatedAt: new Date("2024-01-16").toISOString(),
    isAccountedForRep: false,
    isArchived: false,
    notes: "طلب عادي",
    address: "بغداد - الكرخ - شارع الجمهورية",
    recipientName: "أحمد محمد",
  },
  {
    id: "order-002", 
    orderNumber: "R002",
    companyName: "مؤسسة الأمل",
    mobileNumber: "***********",
    amount: 75000,
    status: "تم التسليم" as OrderStatus,
    agentId: "rep-001",
    createdAt: new Date("2024-01-16").toISOString(),
    updatedAt: new Date("2024-01-17").toISOString(),
    isAccountedForRep: false,
    isArchived: false,
    notes: "طلب مستعجل",
    address: "بغداد - الكرخ - منطقة العلاوي",
    recipientName: "فاطمة علي",
  },
  {
    id: "order-003",
    orderNumber: "R003", 
    companyName: "شركة الفجر",
    mobileNumber: "***********",
    amount: 120000,
    status: "تسليم جزئي" as OrderStatus,
    agentId: "rep-002",
    createdAt: new Date("2024-01-17").toISOString(),
    updatedAt: new Date("2024-01-18").toISOString(),
    isAccountedForRep: false,
    isArchived: false,
    notes: "تم تسليم جزء من الطلب",
    address: "بغداد - الرصافة - شارع فلسطين",
    recipientName: "محمد حسن",
    updatedAmount: 80000,
  },
  {
    id: "order-004",
    orderNumber: "R004",
    companyName: "مكتبة المعرفة",
    mobileNumber: "***********",
    amount: 95000,
    status: "تم التسليم" as OrderStatus,
    agentId: "rep-002",
    createdAt: new Date("2024-01-18").toISOString(),
    updatedAt: new Date("2024-01-19").toISOString(),
    isAccountedForRep: false,
    isArchived: false,
    notes: "كتب ومجلات",
    address: "بغداد - الرصافة - شارع المتنبي",
    recipientName: "سارة أحمد",
  },
  {
    id: "order-005",
    orderNumber: "R005",
    companyName: "صيدلية الشفاء",
    mobileNumber: "***********",
    amount: 65000,
    status: "في الطريق" as OrderStatus,
    agentId: "rep-003",
    createdAt: new Date("2024-01-19").toISOString(),
    updatedAt: new Date("2024-01-19").toISOString(),
    isAccountedForRep: false,
    isArchived: false,
    notes: "أدوية",
    address: "البصرة - شارع الكورنيش",
    recipientName: "علي محمود",
  },
  {
    id: "order-006",
    orderNumber: "R006",
    companyName: "مطعم بغداد",
    mobileNumber: "***********",
    amount: 45000,
    status: "مؤكد" as OrderStatus,
    agentId: "rep-001",
    createdAt: new Date("2024-01-20").toISOString(),
    updatedAt: new Date("2024-01-20").toISOString(),
    isAccountedForRep: false,
    isArchived: false,
    notes: "طعام",
    address: "بغداد - الكرخ - شارع الرشيد",
    recipientName: "خالد سالم",
  },
  // Archived orders
  {
    id: "order-007",
    orderNumber: "R007",
    companyName: "شركة التقنية",
    mobileNumber: "***********",
    amount: 150000,
    status: "مؤرشف" as OrderStatus,
    agentId: "rep-001",
    createdAt: new Date("2024-01-10").toISOString(),
    updatedAt: new Date("2024-01-12").toISOString(),
    isAccountedForRep: true,
    isArchived: true,
    statusBeforeArchive: "تم التسليم",
    notes: "أجهزة كمبيوتر",
    address: "بغداد - الكرخ - شارع الجامعة",
    recipientName: "نور الدين",
  },
  {
    id: "order-008",
    orderNumber: "R008",
    companyName: "معرض الأثاث",
    mobileNumber: "***********",
    amount: 200000,
    status: "مؤرشف" as OrderStatus,
    agentId: "rep-002",
    createdAt: new Date("2024-01-08").toISOString(),
    updatedAt: new Date("2024-01-10").toISOString(),
    isAccountedForRep: true,
    isArchived: true,
    statusBeforeArchive: "تم التسليم",
    notes: "أثاث منزلي",
    address: "بغداد - الرصافة - شارع الصدر",
    recipientName: "ليلى حسن",
  },
];

// Mock accounting records
export const mockAccountingRecords: AccountingRecordRep[] = [
  {
    settlementId: "settlement-001",
    representativeId: "rep-001",
    representativeName: "أحمد علي",
    date: new Date("2024-01-12").toISOString(),
    totalCollectedAmount: 150000,
    totalCommission: 1000,
    netPayableToRep: 149000,
    paymentStatus: "paid",
    orders: [
      {
        id: "R007",
        customerName: "شركة التقنية",
        amount: 150000,
        commission: 1000,
      }
    ],
    creatorId: "admin-001",
  },
  {
    settlementId: "settlement-002",
    representativeId: "rep-002",
    representativeName: "محمد حسن",
    date: new Date("2024-01-10").toISOString(),
    totalCollectedAmount: 200000,
    totalCommission: 1000,
    netPayableToRep: 199000,
    paymentStatus: "pending",
    orders: [
      {
        id: "R008",
        customerName: "معرض الأثاث",
        amount: 200000,
        commission: 1000,
      }
    ],
    creatorId: "admin-001",
  },
];

export class MockOrderData {
  private static orders: Order[] = [...mockOrders];
  private static accountingRecords: AccountingRecordRep[] = [...mockAccountingRecords];

  static async getOrders(filters?: {
    status?: OrderStatus | OrderStatus[];
    agentId?: string;
    isArchived?: boolean;
    isAccountedForRep?: boolean;
    companyName?: string;
    orderNumber?: string;
  }): Promise<Order[]> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    let filteredOrders = [...this.orders];
    
    if (filters?.status) {
      if (Array.isArray(filters.status)) {
        filteredOrders = filteredOrders.filter(o => filters.status!.includes(o.status));
      } else {
        filteredOrders = filteredOrders.filter(o => o.status === filters.status);
      }
    }
    
    if (filters?.agentId) {
      filteredOrders = filteredOrders.filter(o => o.agentId === filters.agentId);
    }
    
    if (filters?.isArchived !== undefined) {
      filteredOrders = filteredOrders.filter(o => o.isArchived === filters.isArchived);
    }
    
    if (filters?.isAccountedForRep !== undefined) {
      filteredOrders = filteredOrders.filter(o => o.isAccountedForRep === filters.isAccountedForRep);
    }
    
    if (filters?.companyName) {
      filteredOrders = filteredOrders.filter(o => 
        o.companyName.toLowerCase().includes(filters.companyName!.toLowerCase())
      );
    }
    
    if (filters?.orderNumber) {
      filteredOrders = filteredOrders.filter(o => 
        o.orderNumber.toLowerCase().includes(filters.orderNumber!.toLowerCase())
      );
    }
    
    return filteredOrders;
  }

  static async getOrder(id: string): Promise<Order | null> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return this.orders.find(o => o.id === id) || null;
  }

  static async updateOrder(id: string, updates: Partial<Order>): Promise<Order | null> {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const orderIndex = this.orders.findIndex(o => o.id === id);
    if (orderIndex === -1) return null;
    
    this.orders[orderIndex] = {
      ...this.orders[orderIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    return this.orders[orderIndex];
  }

  static async addRepSettlement(settlement: Omit<AccountingRecordRep, 'settlementId'>): Promise<AccountingRecordRep> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const newSettlement: AccountingRecordRep = {
      ...settlement,
      settlementId: `settlement-${Date.now()}`,
    };
    
    this.accountingRecords.push(newSettlement);
    return newSettlement;
  }

  static async getRepSettlement(settlementId: string): Promise<AccountingRecordRep | null> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return this.accountingRecords.find(r => r.settlementId === settlementId) || null;
  }

  static async getRepSettlements(filters?: {
    representativeId?: string;
    paymentStatus?: string;
  }): Promise<AccountingRecordRep[]> {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    let filtered = [...this.accountingRecords];
    
    if (filters?.representativeId) {
      filtered = filtered.filter(r => r.representativeId === filters.representativeId);
    }
    
    if (filters?.paymentStatus) {
      filtered = filtered.filter(r => r.paymentStatus === filters.paymentStatus);
    }
    
    return filtered;
  }
}
