"use client";

import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export default function OrdersPage() {
  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader 
        title="إدارة الطلبات" 
        description="عرض وإضافة وتعديل الطلبات"
      >
        <Button>
          <Plus className="h-4 w-4 ml-2" />
          طلب جديد
        </Button>
      </PageHeader>
      
      <Card>
        <CardHeader>
          <CardTitle>قائمة الطلبات</CardTitle>
          <CardDescription>
            جميع الطلبات المسجلة في النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            سيتم إضافة قائمة الطلبات هنا...
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
