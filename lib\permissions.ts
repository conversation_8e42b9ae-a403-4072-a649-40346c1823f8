export type AppSectionId = 
  | "orders" 
  | "dispatch" 
  | "returns" 
  | "accounting" 
  | "archive" 
  | "users" 
  | "importExport" 
  | "notifications" 
  | "imageShare" 
  | "settings";

export interface Permission {
  view: boolean;
  create: boolean;
  edit: boolean;
  delete: boolean;
}

export type UserPermissions = Record<AppSectionId, Permission>;

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'employee';
  permissions: UserPermissions;
  isActive: boolean;
  createdAt: Date;
  lastLogin?: Date;
}

// Default permissions for different roles
export const defaultPermissions: Record<User['role'], UserPermissions> = {
  admin: {
    orders: { view: true, create: true, edit: true, delete: true },
    dispatch: { view: true, create: true, edit: true, delete: true },
    returns: { view: true, create: true, edit: true, delete: true },
    accounting: { view: true, create: true, edit: true, delete: true },
    archive: { view: true, create: true, edit: true, delete: true },
    users: { view: true, create: true, edit: true, delete: true },
    importExport: { view: true, create: true, edit: true, delete: true },
    notifications: { view: true, create: true, edit: true, delete: true },
    imageShare: { view: true, create: true, edit: true, delete: true },
    settings: { view: true, create: true, edit: true, delete: true },
  },
  manager: {
    orders: { view: true, create: true, edit: true, delete: false },
    dispatch: { view: true, create: true, edit: true, delete: false },
    returns: { view: true, create: true, edit: true, delete: false },
    accounting: { view: true, create: false, edit: false, delete: false },
    archive: { view: true, create: false, edit: false, delete: false },
    users: { view: true, create: false, edit: false, delete: false },
    importExport: { view: true, create: true, edit: false, delete: false },
    notifications: { view: true, create: true, edit: true, delete: false },
    imageShare: { view: true, create: true, edit: true, delete: false },
    settings: { view: true, create: false, edit: true, delete: false },
  },
  employee: {
    orders: { view: true, create: true, edit: true, delete: false },
    dispatch: { view: true, create: false, edit: false, delete: false },
    returns: { view: true, create: true, edit: true, delete: false },
    accounting: { view: false, create: false, edit: false, delete: false },
    archive: { view: true, create: false, edit: false, delete: false },
    users: { view: false, create: false, edit: false, delete: false },
    importExport: { view: false, create: false, edit: false, delete: false },
    notifications: { view: true, create: false, edit: false, delete: false },
    imageShare: { view: true, create: true, edit: true, delete: false },
    settings: { view: true, create: false, edit: true, delete: false },
  },
};

export function hasPermission(
  userPermissions: UserPermissions,
  section: AppSectionId,
  action: keyof Permission
): boolean {
  return userPermissions[section]?.[action] ?? false;
}

export function createUser(
  userData: Omit<User, 'id' | 'permissions' | 'createdAt'>,
  customPermissions?: Partial<UserPermissions>
): User {
  const basePermissions = defaultPermissions[userData.role];
  const permissions = customPermissions 
    ? { ...basePermissions, ...customPermissions }
    : basePermissions;

  return {
    ...userData,
    id: Math.random().toString(36).substr(2, 9),
    permissions,
    createdAt: new Date(),
  };
}
