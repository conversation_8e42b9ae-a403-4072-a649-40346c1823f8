pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
    }
}

rootProject.name = "Rihla Delivery"

include ':app'
include ':capacitor-cordova-android-plugins'
project(':capacitor-cordova-android-plugins').projectDir = new File('./capacitor-cordova-android-plugins/')

include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../node_modules/@capacitor/android/capacitor')

// Include any additional Capacitor plugins
def capacitorPlugins = [
    'capacitor-camera',
    'capacitor-filesystem', 
    'capacitor-geolocation',
    'capacitor-network',
    'capacitor-share',
    'capacitor-splash-screen',
    'capacitor-status-bar',
    'capacitor-toast',
    'capacitor-device',
    'capacitor-app',
    'capacitor-haptics',
    'capacitor-keyboard',
    'capacitor-preferences',
    'capacitor-push-notifications'
]

capacitorPlugins.each { plugin ->
    def pluginDir = new File("../node_modules/@capacitor/${plugin}/android")
    if (pluginDir.exists()) {
        include ":${plugin}"
        project(":${plugin}").projectDir = pluginDir
    }
}
