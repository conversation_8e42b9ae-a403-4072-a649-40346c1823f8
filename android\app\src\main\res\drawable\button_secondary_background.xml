<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/button_secondary_pressed" />
            <stroke android:width="1dp" android:color="@color/border" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/gray_200" />
            <stroke android:width="1dp" android:color="@color/gray_300" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/button_secondary" />
            <stroke android:width="1dp" android:color="@color/border" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
