#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo "========================================"
echo "       تطبيق Rihla Delivery"
echo "    نظام إدارة الطلبات والتوصيل"
echo "========================================"
echo

show_menu() {
    echo "اختر طريقة تشغيل التطبيق:"
    echo
    echo "[1] تشغيل على المتصفح (Web)"
    echo "[2] تشغيل على الأندرويد (APK)"
    echo "[3] تشغيل على iOS (Simulator)"
    echo "[4] بناء التطبيق للإنتاج"
    echo "[5] تثبيت المكتبات"
    echo "[6] إعداد Firebase"
    echo "[7] تشغيل Firebase Emulators"
    echo "[8] إنشاء APK للتوزيع"
    echo "[9] خروج"
    echo
}

run_web() {
    echo
    echo "========================================"
    echo "      تشغيل التطبيق على المتصفح"
    echo "========================================"
    echo
    echo "جاري تشغيل خادم التطوير..."
    echo "سيتم فتح التطبيق على: http://localhost:3000"
    echo
    echo "للإيقاف اضغط Ctrl+C"
    echo
    npm run dev
}

run_android() {
    echo
    echo "========================================"
    echo "      تشغيل التطبيق على الأندرويد"
    echo "========================================"
    echo
    echo "تأكد من:"
    echo "- تشغيل Android Studio"
    echo "- توصيل جهاز أندرويد أو تشغيل المحاكي"
    echo "- تفعيل USB Debugging"
    echo
    echo "جاري بناء التطبيق..."
    npm run build
    echo
    echo "جاري مزامنة Capacitor..."
    npx cap sync android
    echo
    echo "جاري فتح Android Studio..."
    npx cap open android
    echo
    echo "تعليمات:"
    echo "1. في Android Studio، اضغط على زر Run"
    echo "2. اختر الجهاز أو المحاكي"
    echo "3. انتظر حتى يتم تثبيت التطبيق"
    echo
}

run_ios() {
    echo
    echo "========================================"
    echo "        تشغيل التطبيق على iOS"
    echo "========================================"
    echo
    echo "تأكد من:"
    echo "- تشغيل Xcode على macOS"
    echo "- تشغيل iOS Simulator"
    echo
    echo "جاري بناء التطبيق..."
    npm run build
    echo
    echo "جاري مزامنة Capacitor..."
    npx cap sync ios
    echo
    echo "جاري فتح Xcode..."
    npx cap open ios
    echo
    echo "تعليمات:"
    echo "1. في Xcode، اختر المحاكي"
    echo "2. اضغط على زر Run"
    echo "3. انتظر حتى يتم تشغيل التطبيق"
    echo
}

build_production() {
    echo
    echo "========================================"
    echo "       بناء التطبيق للإنتاج"
    echo "========================================"
    echo
    echo "جاري بناء التطبيق..."
    npm run build
    echo
    echo "جاري إنشاء ملفات التطبيق المحمول..."
    npx cap sync
    echo
    echo "تم بناء التطبيق بنجاح!"
    echo "الملفات موجودة في مجلد: out/"
    echo
}

install_dependencies() {
    echo
    echo "========================================"
    echo "         تثبيت المكتبات"
    echo "========================================"
    echo
    echo "جاري تثبيت المكتبات الأساسية..."
    npm install
    echo
    echo "جاري تثبيت مكتبات Firebase..."
    npm install firebase
    echo
    echo "جاري تثبيت مكتبات Capacitor..."
    npm install @capacitor/core @capacitor/cli
    npm install @capacitor/android @capacitor/ios
    echo
    echo "جاري تثبيت مكتبات UI..."
    npm install @radix-ui/react-progress --legacy-peer-deps
    echo
    echo "تم تثبيت جميع المكتبات بنجاح!"
    echo
}

setup_firebase() {
    echo
    echo "========================================"
    echo "          إعداد Firebase"
    echo "========================================"
    echo
    echo "تأكد من إنشاء مشروع Firebase أولاً على:"
    echo "https://console.firebase.google.com"
    echo
    echo "جاري تثبيت Firebase CLI..."
    npm install -g firebase-tools
    echo
    echo "جاري تسجيل الدخول إلى Firebase..."
    firebase login
    echo
    echo "جاري ربط المشروع..."
    firebase init
    echo
    echo "جاري رفع قواعد الأمان..."
    firebase deploy --only firestore:rules
    echo
    echo "تم إعداد Firebase بنجاح!"
    echo "لا تنس إضافة متغيرات البيئة في ملف .env.local"
    echo
}

run_firebase_emulators() {
    echo
    echo "========================================"
    echo "       تشغيل Firebase Emulators"
    echo "========================================"
    echo
    echo "جاري تشغيل محاكيات Firebase..."
    echo "سيتم فتح واجهة الإدارة على: http://localhost:4000"
    echo
    firebase emulators:start
}

build_apk() {
    echo
    echo "========================================"
    echo "        إنشاء APK للتوزيع"
    echo "========================================"
    echo
    echo "جاري بناء التطبيق..."
    npm run build
    echo
    echo "جاري مزامنة Capacitor..."
    npx cap sync android
    echo
    echo "تعليمات إنشاء APK:"
    echo "1. افتح Android Studio"
    echo "2. اذهب إلى Build > Generate Signed Bundle / APK"
    echo "3. اختر APK"
    echo "4. أنشئ مفتاح جديد أو استخدم موجود"
    echo "5. اختر release build"
    echo "6. انتظر حتى يتم إنشاء APK"
    echo
    echo "سيتم حفظ APK في:"
    echo "android/app/build/outputs/apk/release/"
    echo
    npx cap open android
}

# الحلقة الرئيسية
while true; do
    show_menu
    read -p "أدخل اختيارك (1-9): " choice
    
    case $choice in
        1)
            run_web
            ;;
        2)
            run_android
            ;;
        3)
            run_ios
            ;;
        4)
            build_production
            ;;
        5)
            install_dependencies
            ;;
        6)
            setup_firebase
            ;;
        7)
            run_firebase_emulators
            ;;
        8)
            build_apk
            ;;
        9)
            echo
            echo "شكراً لاستخدام تطبيق Rihla Delivery!"
            echo
            exit 0
            ;;
        *)
            echo "اختيار غير صحيح. يرجى المحاولة مرة أخرى."
            ;;
    esac
    
    echo
    read -p "اضغط Enter للعودة إلى القائمة الرئيسية..."
done
