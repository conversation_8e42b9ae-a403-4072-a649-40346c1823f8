# دليل التشغيل السريع - تطبيق Rihla Delivery

## 🚀 التشغيل السريع (أسهل طريقة)

### Windows
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع
3. شغل الأمر: `run-app.bat`
4. اختر الخيار المناسب من القائمة

### Linux/Mac
1. افتح Terminal
2. انتقل إلى مجلد المشروع
3. شغل الأمر: `chmod +x run-app.sh && ./run-app.sh`
4. اختر الخيار المناسب من القائمة

## 📱 خيارات التشغيل

### 1. تشغيل على المتصفح (الأسرع)
- اختر الخيار `[1]` من القائمة
- أو شغل: `npm run dev`
- افتح: http://localhost:3000

### 2. تشغيل على الأندرويد
- اختر الخيار `[2]` من القائمة
- تأكد من تثبيت Android Studio
- وصل جهاز أندرويد أو شغل المحاكي

### 3. تشغيل على iOS (Mac فقط)
- اختر الخيار `[3]` من القائمة
- تأكد من تثبيت Xcode
- شغل iOS Simulator

### 4. إنشاء APK للتوزيع
- اختر الخيار `[8]` من القائمة
- سيتم فتح Android Studio
- اتبع التعليمات لإنشاء APK

## 🔧 إعداد Firebase (اختياري)

### الخطوات السريعة:
1. اختر الخيار `[6]` من القائمة
2. أو اتبع هذه الخطوات:

```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# إعداد المشروع
firebase init

# رفع القواعد
firebase deploy --only firestore:rules
```

### إعداد متغيرات البيئة:
1. انسخ ملف `.env.local.example` إلى `.env.local`
2. املأ بيانات Firebase من Console

## 🛠️ حل المشاكل الشائعة

### مشكلة: npm command not found
**الحل:** تثبيت Node.js من https://nodejs.org

### مشكلة: permission denied (Linux/Mac)
**الحل:** `chmod +x run-app.sh`

### مشكلة: Android Studio لا يفتح
**الحل:** تأكد من تثبيت Android Studio وإضافته للـ PATH

### مشكلة: Firebase errors
**الحل:** تأكد من إعداد Firebase وملف .env.local

### مشكلة: Port 3000 already in use
**الحل:** أغلق التطبيقات الأخرى أو استخدم port آخر:
```bash
npm run dev -- --port 3001
```

## 📋 بيانات تجريبية للدخول

بعد تشغيل التطبيق، يمكنك الدخول باستخدام:

- **المدير**: <EMAIL> / password
- **المشرف**: <EMAIL> / password  
- **الموظف**: <EMAIL> / password

## 🎯 الميزات الرئيسية

- ✅ إدارة الطلبات والتوصيل
- ✅ نظام المستخدمين والصلاحيات
- ✅ تتبع المندوبين والعملاء
- ✅ التقارير والإحصائيات
- ✅ استيراد/تصدير Excel
- ✅ دعم الأندرويد و iOS
- ✅ واجهة عربية متجاوبة
- ✅ دعم Firebase (اختياري)

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md للتفاصيل الكاملة
2. تأكد من تثبيت جميع المتطلبات
3. جرب إعادة تثبيت المكتبات: `npm install`
4. تحقق من إعدادات Firebase إذا كنت تستخدمه

## 🔄 تحديث التطبيق

```bash
# سحب آخر التحديثات
git pull

# تحديث المكتبات
npm install

# إعادة بناء التطبيق
npm run build
```

---

**نصيحة:** استخدم الملف التفاعلي `run-app.bat` أو `run-app.sh` لأسهل تجربة!
