package com.rihla.delivery;

import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.view.WindowManager;
import android.content.Intent;
import android.net.Uri;

import com.getcapacitor.BridgeActivity;
import com.getcapacitor.Plugin;

import java.util.ArrayList;

public class MainActivity extends BridgeActivity {
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Configure WebView settings for better performance
        configureWebView();
        
        // Handle app launch from deep links
        handleIntent(getIntent());
        
        // Set status bar color
        getWindow().setStatusBarColor(getResources().getColor(R.color.primary_dark));
        
        // Keep screen on during important operations (optional)
        // getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        
        // Register additional plugins if needed
        registerAdditionalPlugins();
    }
    
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        handleIntent(intent);
    }
    
    private void configureWebView() {
        WebView webView = getBridge().getWebView();
        WebSettings webSettings = webView.getSettings();
        
        // Enable JavaScript (already enabled by Capacitor)
        webSettings.setJavaScriptEnabled(true);
        
        // Enable DOM storage
        webSettings.setDomStorageEnabled(true);
        
        // Enable database storage
        webSettings.setDatabaseEnabled(true);
        
        // Enable app cache
        webSettings.setAppCacheEnabled(true);
        
        // Set cache mode for better offline support
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        
        // Enable mixed content for development
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE);
        
        // Set user agent string
        String userAgent = webSettings.getUserAgentString();
        webSettings.setUserAgentString(userAgent + " RihlaDeliveryApp/1.0");
        
        // Enable hardware acceleration
        webView.setLayerType(WebView.LAYER_TYPE_HARDWARE, null);
        
        // Set text zoom
        webSettings.setTextZoom(100);
        
        // Enable zoom controls (optional)
        webSettings.setSupportZoom(false);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);
        
        // Enable geolocation
        webSettings.setGeolocationEnabled(true);
        
        // Set media playback settings
        webSettings.setMediaPlaybackRequiresUserGesture(false);
        
        // Enable safe browsing
        webSettings.setSafeBrowsingEnabled(true);
    }
    
    private void handleIntent(Intent intent) {
        if (intent != null) {
            String action = intent.getAction();
            Uri data = intent.getData();
            
            if (Intent.ACTION_VIEW.equals(action) && data != null) {
                // Handle deep links
                String url = data.toString();
                if (url.startsWith("https://rihla-delivery.app")) {
                    // Navigate to specific page based on deep link
                    getBridge().getWebView().loadUrl("javascript:window.location.href='" + url + "'");
                }
            }
            
            // Handle other intent actions if needed
            if (Intent.ACTION_SEND.equals(action)) {
                // Handle shared content
                handleSharedContent(intent);
            }
        }
    }
    
    private void handleSharedContent(Intent intent) {
        String type = intent.getType();
        if (type != null) {
            if (type.startsWith("text/")) {
                String sharedText = intent.getStringExtra(Intent.EXTRA_TEXT);
                if (sharedText != null) {
                    // Handle shared text
                    getBridge().getWebView().loadUrl("javascript:window.handleSharedText('" + sharedText + "')");
                }
            } else if (type.startsWith("image/")) {
                Uri imageUri = intent.getParcelableExtra(Intent.EXTRA_STREAM);
                if (imageUri != null) {
                    // Handle shared image
                    getBridge().getWebView().loadUrl("javascript:window.handleSharedImage('" + imageUri.toString() + "')");
                }
            }
        }
    }
    
    private void registerAdditionalPlugins() {
        // Register any additional Capacitor plugins here
        // Example:
        // this.init(savedInstanceState, new ArrayList<Class<? extends Plugin>>() {{
        //     add(SomePlugin.class);
        // }});
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // App resumed - you can add analytics or other tracking here
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // App paused - save any important state here
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up resources
    }
    
    @Override
    public void onBackPressed() {
        // Handle back button press
        WebView webView = getBridge().getWebView();
        if (webView.canGoBack()) {
            // Let the web app handle navigation
            webView.loadUrl("javascript:window.history.back()");
        } else {
            // Exit app or show exit confirmation
            super.onBackPressed();
        }
    }
}
