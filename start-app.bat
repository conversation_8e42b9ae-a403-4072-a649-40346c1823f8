@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    🚀 Rihla Delivery - تشغيل سريع
echo ========================================
echo.

:: Check if Node.js is installed
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً.
    echo    تحميل من: https://nodejs.org
    pause
    exit /b 1
)

:: Check if npm is available
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح. يرجى التأكد من تثبيت Node.js بشكل صحيح.
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo ✅ npm متاح
echo.

:: Check if node_modules exists
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

echo 🚀 تشغيل التطبيق...
echo.
echo 🌐 سيتم فتح التطبيق على: http://localhost:3000
echo.
echo 🔑 بيانات الدخول:
echo ┌─────────────────────────────────────────┐
echo │ 🏢 الإدارة العليا:                      │
echo │   • المدير العام: admin / password     │
echo │   • المشرف: manager / password          │
echo │   • الموظف: employee / password         │
echo │                                         │
echo │ 🚚 المندوبين:                          │
echo │   • المندوب الأول: rep1 / password     │
echo │   • المندوب الثاني: rep2 / password    │
echo │   • المندوب الثالث: rep3 / password    │
echo │                                         │
echo │ 💼 الأدوار الأخرى:                     │
echo │   • المحاسب: accountant1 / password    │
echo │                                         │
echo │ ⭐ الحساب الافتراضي:                   │
echo │   • default / 123456                   │
echo └─────────────────────────────────────────┘
echo.

:: Start the development server
start "" "http://localhost:3000"
call npm run dev

echo.
echo 🛑 تم إيقاف التطبيق
pause
