"use client";

import React, { useState, useMemo, useEffect, useCallback } from "react";
import { PageHeader } from "@/components/page-header";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Send, Users2, PackageSearch, ArrowRight, Loader2, Filter } from "lucide-react";
import type { Order, User } from "@/types";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { getUsers } from "@/lib/user-data";
import { getOrders, updateOrder } from "@/lib/order-data";
import { useToast } from "@/hooks/use-toast";

export default function DispatchToAgentPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAuth();
  
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [searchInput, setSearchInput] = useState("");
  
  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
        const [orders, users] = await Promise.all([getOrders(), getUsers()]);
        setAllOrders(orders);
        setAllUsers(users);
    } catch (error) {
        toast({
            variant: "destructive",
            title: "خطأ في تحميل البيانات",
            description: "فشل تحميل الطلبات والمستخدمين من قاعدة البيانات.",
        });
        console.error("Failed to fetch data:", error);
    } finally {
        setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    if (currentUser) {
        fetchData();
    }
  }, [currentUser, fetchData]);

  const availableAgents = useMemo(() => {
    if (!currentUser) return [];
    
    return allUsers.filter(u => u.role === 'مندوب' && u.isActive);
  }, [currentUser, allUsers]);

  const unassignedOrders = useMemo(() => {
      if (!currentUser) return [];
      
      return allOrders.filter(o => 
        (o.status === 'في الانتظار' || o.status === 'مؤكد') && 
        !o.agentId && 
        !o.isArchived
      );
  }, [allOrders, currentUser]);

  const displayedUnassignedOrders = useMemo(() => {
    return unassignedOrders.filter(order => 
      (searchInput === "" || 
       order.orderNumber.toLowerCase().includes(searchInput.toLowerCase()) || 
       order.mobileNumber.includes(searchInput) ||
       order.companyName.toLowerCase().includes(searchInput.toLowerCase()))
    );
  }, [searchInput, unassignedOrders]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-IQ', { style: 'currency', currency: 'IQD', minimumFractionDigits: 0 }).format(amount);
  };

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(orderId)) newSelected.delete(orderId);
      else newSelected.add(orderId);
      return newSelected;
    });
  };

  const handleSelectAll = (check: boolean | 'indeterminate') => {
    if(check) {
      setSelectedOrders(new Set(displayedUnassignedOrders.map(o => o.id)));
    } else {
      setSelectedOrders(new Set());
    }
  };
  
  const handleDispatch = async () => {
    if (selectedOrders.size === 0 || !selectedAgentId || !currentUser) {
      toast({ variant: 'destructive', title: "خطأ", description: "يرجى اختيار مندوب وتحديد طلب واحد على الأقل للإسناد." });
      return;
    }
    const targetAgent = availableAgents.find(a => a.id === selectedAgentId);
    if (!targetAgent) return;
    
    const updatePromises = Array.from(selectedOrders).map(orderId => {
        return updateOrder(orderId, {
            agentId: selectedAgentId,
            status: 'قيد التوصيل',
            updatedAt: new Date().toISOString(),
            lastUpdatedById: currentUser.id,
        });
    });

    try {
        await Promise.all(updatePromises);
        toast({ title: 'تم الإسناد', description: `تم إسناد ${selectedOrders.size} طلبات إلى ${targetAgent.name}.` });
        setSelectedOrders(new Set());
        setSearchInput("");
        fetchData(); // Re-fetch data to show the updated list
    } catch (error) {
        toast({ variant: 'destructive', title: "خطأ", description: "فشل إسناد الطلبات. يرجى المحاولة مرة أخرى." });
        console.error("Failed to dispatch orders:", error);
    }
  };

  const handleSearchKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && searchInput.trim() !== "") {
      event.preventDefault();
      const currentSearchTerm = searchInput.trim().toLowerCase();
      const orderToSelect = displayedUnassignedOrders.find(o => o.orderNumber.toLowerCase() === currentSearchTerm);
            
      if (orderToSelect && !selectedOrders.has(orderToSelect.id)) {
        handleSelectOrder(orderToSelect.id);
      } else {
        toast({variant: "destructive", title: "خطأ", description: `لم يتم العثور على طلب برقم الوصل: ${searchInput.trim()} ضمن الطلبات المعروضة.`});
      }
      setSearchInput(""); 
    }
  };

  if (isLoading || !currentUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mr-4">جار تحميل البيانات...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="إسناد الطلبات إلى المندوبين"
        description="اختر مندوبًا ثم قم بإسناد الطلبات الجديدة أو غير المسندة إليه."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button asChild variant="outline">
                <Link href="/dispatch"><ArrowRight className="h-4 w-4" /> رجوع</Link>
            </Button>
          </div>
        }
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1 space-y-6">
            <Card className="shadow-lg">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><Filter className="h-5 w-5 text-primary"/> لوحة التحكم</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <label htmlFor="agent-select" className="text-sm font-medium mb-1 block">1. اختر المندوب</label>
                        <Select onValueChange={setSelectedAgentId} value={selectedAgentId || ""}>
                            <SelectTrigger id="agent-select">
                            <SelectValue placeholder="اختر مندوب" />
                            </SelectTrigger>
                            <SelectContent>
                            {availableAgents.length > 0 ? (
                                availableAgents.map(agent => (
                                <SelectItem key={agent.id} value={agent.id}>{agent.name}</SelectItem>
                                ))
                            ) : (
                                <div className="p-4 text-center text-sm text-muted-foreground">لا يوجد مندوبون.</div>
                            )}
                            </SelectContent>
                        </Select>
                    </div>
                     <div>
                        <label htmlFor="search-input" className="text-sm font-medium mb-1 block">2. بحث وتحديد (اختياري)</label>
                         <Input 
                            id="search-input"
                            placeholder="بحث بالوصل أو الهاتف..." 
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            onKeyDown={handleSearchKeyDown}
                        />
                         <p className="text-xs text-muted-foreground mt-1">اضغط Enter على رقم الوصل لتحديد سريع.</p>
                    </div>
                </CardContent>
                <CardFooter>
                     <Button 
                        onClick={handleDispatch} 
                        disabled={selectedOrders.size === 0 || !selectedAgentId}
                        className="w-full"
                        >
                        <Send className="ml-2 h-4 w-4" />
                        إسناد الطلبات ({selectedOrders.size})
                    </Button>
                </CardFooter>
            </Card>
        </div>

        <div className="lg:col-span-2">
            <Card className="shadow-lg">
            <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2">
                        <PackageSearch className="h-6 w-6 text-primary"/> الطلبات غير المسندة ({displayedUnassignedOrders.length})
                    </CardTitle>
                     <Button variant="outline" size="sm" onClick={() => handleSelectAll(selectedOrders.size !== displayedUnassignedOrders.length)}>
                        {selectedOrders.size === displayedUnassignedOrders.length ? "إلغاء تحديد الكل" : "تحديد الكل"}
                    </Button>
                </div>
                <CardDescription>
                    اختر الطلبات من القائمة أدناه لإسنادها للمندوب المحدد.
                </CardDescription>
            </CardHeader>
            <CardContent>
                {displayedUnassignedOrders.length === 0 ? (
                    <div className="text-center py-20 text-muted-foreground">
                        <PackageSearch className="mx-auto h-12 w-12 mb-4" />
                        <p className="text-lg font-semibold">
                            {searchInput ? "لا توجد طلبات تطابق بحثك." : "لا توجد طلبات متاحة للإسناد حالياً."}
                        </p>
                    </div>
                    ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 max-h-[70vh] overflow-y-auto p-2">
                        {displayedUnassignedOrders.map((order) => (
                            <Card key={order.id} className={`shadow-sm hover:shadow-md transition-shadow relative ${selectedOrders.has(order.id) ? 'ring-2 ring-primary' : ''}`}>
                                <Checkbox
                                    checked={selectedOrders.has(order.id)}
                                    onCheckedChange={() => handleSelectOrder(order.id)}
                                    aria-label={`تحديد الطلب ${order.orderNumber}`}
                                    className="absolute top-3 right-3 h-5 w-5"
                                />
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-base font-semibold">{order.orderNumber}</CardTitle>
                                </CardHeader>
                                <CardContent className="text-sm space-y-1.5 pt-0 pb-3 text-muted-foreground">
                                    <p><strong>الشركة:</strong> <span className="text-foreground">{order.companyName}</span></p>
                                    <p><strong>الهاتف:</strong> <span className="text-foreground" dir="ltr">{order.mobileNumber}</span></p>
                                    <p><strong>العنوان:</strong> <span className="text-foreground">{order.address}</span></p>
                                    <p><strong>المبلغ:</strong> <span className="text-foreground font-semibold">{formatCurrency(order.amount)}</span></p>
                                    <p><strong>تاريخه:</strong> <span className="text-foreground">{new Date(order.createdAt).toLocaleDateString('ar-IQ')}</span></p>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                    )
                }
            </CardContent>
            </Card>
        </div>
      </div>
    </div>
  );
}
