"use client";

import React, {useState, use<PERSON><PERSON><PERSON>, useEffect, use<PERSON><PERSON>back} from "react";
import { PageHeader } from "@/components/page-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import type { Order, User, AccountingRecordRep } from "@/types";
import { Users, Printer, DollarSign, ArrowRight, Home, Loader2, FileText, Filter } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/auth-context";
import { getUsers } from "@/lib/user-data";
import { getOrders, addRepSettlement, updateOrder } from "@/lib/order-data";

export default function AgentAccountingPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAuth();
  
  const [ordersForSettlement, setOrdersForSettlement] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFetchingOrders, setIsFetchingOrders] = useState(false);

  const [availableAgents, setAvailableAgents] = useState<User[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isStatementDialogOpen, setIsStatementDialogOpen] = useState(false);

  const fetchInitialData = useCallback(async () => {
    setIsLoading(true);
    if (!currentUser) {
        setIsLoading(false);
        return;
    }
    try {
        const filters: any = { role: 'مندوب' };
        const agentsToShow = await getUsers(filters);
        setAvailableAgents(agentsToShow);

    } catch (error) {
        toast({variant: "destructive", title: "خطأ", description: "فشل تحميل قائمة المندوبين."});
        console.error("User fetching error:", error);
    } finally {
        setIsLoading(false);
    }
  }, [currentUser, toast]);

  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  useEffect(() => {
    const fetchOrdersForAgent = async () => {
        if (!selectedAgentId) {
            setOrdersForSettlement([]);
            return;
        }
        setIsFetchingOrders(true);
        try {
            const orders = await getOrders({ 
                agentId: selectedAgentId,
                status: ["تم التسليم", "تسليم جزئي"],
                isAccountedForRep: false
            });
            setOrdersForSettlement(orders);
        } catch (error) {
            toast({variant: "destructive", title: "خطأ", description: "فشل تحميل طلبات المندوب."});
            console.error("Order fetching error:", error);
        } finally {
            setIsFetchingOrders(false);
        }
    };
    
    fetchOrdersForAgent();
  }, [selectedAgentId, toast]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-IQ', { style: 'currency', currency: 'IQD', minimumFractionDigits: 0 }).format(amount);
  };

  const commissionPerOrder = 1000;

  const displayedOrders = useMemo(() => {
    if (searchTerm === "") return ordersForSettlement;
    return ordersForSettlement.filter(o => 
         o.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
         o.mobileNumber.includes(searchTerm)
    );
  }, [ordersForSettlement, searchTerm]);

  const selectedOrdersData = displayedOrders.filter(order => selectedRows.has(order.id));
  const totalSelectedAmount = selectedOrdersData.reduce((sum, order) => sum + (order.updatedAmount ?? order.amount), 0);
  const totalCommission = selectedOrdersData.length * commissionPerOrder;
  const netPayable = totalSelectedAmount - totalCommission;

  const handleSelectAll = (checked: boolean | 'indeterminate') => {
    if (!selectedAgentId || displayedOrders.length === 0) return;
    if (checked === true) { 
      setSelectedRows(new Set(displayedOrders.map(o => o.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleRowSelect = (orderId: string) => {
    setSelectedRows(prev => {
        const newSet = new Set(prev);
        if (newSet.has(orderId)) newSet.delete(orderId);
        else newSet.add(orderId);
        return newSet;
    });
  };

  const handleConfirmAccounting = async () => {
    if (selectedRows.size === 0 || !selectedAgentId || !currentUser) {
        toast({ variant: "destructive", title: "خطأ", description: "يرجى تحديد مندوب وطلب واحد على الأقل لتأكيد المحاسبة."});
        return;
    }
    
    const agentDetails = availableAgents.find(a => a.id === selectedAgentId);

    const newSettlement: Omit<AccountingRecordRep, 'settlementId'> = {
        representativeId: selectedAgentId,
        representativeName: agentDetails?.name || selectedAgentId,
        date: new Date().toISOString(),
        totalCollectedAmount: totalSelectedAmount,
        totalCommission: totalCommission,
        netPayableToRep: netPayable,
        paymentStatus: 'pending',
        orders: selectedOrdersData.map(o => ({
            id: o.orderNumber,
            customerName: o.companyName, 
            amount: o.amount,
            updatedAmount: o.updatedAmount,
            commission: commissionPerOrder,
        })),
        creatorId: currentUser.id,
    };
    
    try {
        await addRepSettlement(newSettlement);
        
        const updatePromises = selectedOrdersData.map(order => 
            updateOrder(order.id, {
                isAccountedForRep: true,
                isArchived: true,
                statusBeforeArchive: order.status,
                updatedAt: new Date().toISOString(),
                lastUpdatedById: currentUser.id,
            })
        );
        await Promise.all(updatePromises);
        
        toast({ title: "تم", description: `تم تأكيد وحفظ وأرشفة تسوية المندوب لـ ${selectedRows.size} طلبات. الصافي المستحق: ${formatCurrency(netPayable)}.`});
        
        setSelectedRows(new Set());
        // Re-fetch orders for the current agent to update the list
        if (selectedAgentId) {
            const orders = await getOrders({
                agentId: selectedAgentId,
                status: ["تم التسليم", "تسليم جزئي"],
                isAccountedForRep: false
            });
            setOrdersForSettlement(orders);
        }
    } catch(e) {
        toast({ variant: "destructive", title: "خطأ", description: "فشل حفظ التسوية." });
    }
  }

  const handlePrintStatement = () => {
    if (selectedRows.size === 0 || !selectedAgentId) {
      toast({ variant: "destructive", title: "خطأ", description: "يرجى تحديد مندوب وطلبات للمحاسبة لطباعة الكشف."});
      return;
    }
    setIsStatementDialogOpen(true);
  };
  
  const triggerSystemPrint = () => {
    const contentElement = document.querySelector('.receipt-dialog-content .printable-content');
    if (!contentElement) {
      toast({ variant: "destructive", title: "خطأ في الطباعة", description: "لم يتم العثور على محتوى للطباعة."});
      return;
    }

    const printWindow = window.open('', '', 'height=800,width=1000');
    if (!printWindow) {
      toast({ variant: "destructive", title: "خطأ في الطباعة", description: "تعذر فتح نافذة الطباعة. يرجى التأكد من تعطيل مانع النوافذ المنبثقة.", });
      return;
    }

    const stylesheets = Array.from(document.styleSheets).map(sheet => sheet.href ? `<link rel="stylesheet" href="${sheet.href}">` : '').join('\n');
    const styleTags = Array.from(document.head.getElementsByTagName("style")).map(tag => tag.outerHTML).join('\n');
    const clonedContent = contentElement.cloneNode(true) as HTMLElement;
    clonedContent.querySelectorAll('.no-print').forEach(el => el.remove());

    printWindow.document.write(`
        <html>
            <head>
                <title>كشف محاسبة المندوب</title>
                ${stylesheets}
                ${styleTags}
                <style>
                    @page { size: A4 portrait; margin: 15mm; }
                    body { font-family: var(--font-body), 'Tajawal', sans-serif !important; direction: rtl; background: #fff !important; color: #000 !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important; }
                    .no-print { display: none !important; }
                    table { width: 100% !important; border-collapse: collapse !important; margin-top: 15px !important; font-size: 10pt !important; }
                    th, td { border: 1px solid #666 !important; padding: 6px 8px !important; text-align: right !important; color: black !important; background-color: white !important; }
                    thead { background-color: #e0e0e0 !important; color: black !important; -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important; }
                </style>
            </head>
            <body dir="rtl">
                ${clonedContent.innerHTML}
            </body>
        </html>
    `);
    
    printWindow.document.close();
    setTimeout(() => {
        if (printWindow && !printWindow.closed) {
            printWindow.focus();
            printWindow.print();
        }
    }, 1000);
  }

  if (isLoading) {
    return (
        <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="mr-4">جار تحميل البيانات...</p>
        </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="محاسبة المندوبين"
        description="اختر مندوب لعرض طلباته المستحقة للمحاسبة (تم التسليم، تسليم جزئي)."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button asChild variant="outline">
              <Link href="/accounting">
                <ArrowRight className="h-4 w-4" /> رجوع
              </Link>
            </Button>
          </div>
        }
      />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1 space-y-6">
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-primary"/>
                خيارات التصفية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label htmlFor="agent-select" className="text-sm font-medium mb-1 block">اختر المندوب</label>
                <Select onValueChange={(value) => {setSelectedAgentId(value); setSelectedRows(new Set()); setSearchTerm("");}} value={selectedAgentId || ""}>
                  <SelectTrigger id="agent-select">
                    <SelectValue placeholder="اختر المندوب" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableAgents.map(entity => (
                      <SelectItem key={entity.id} value={entity.id}>{entity.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label htmlFor="search-input" className="text-sm font-medium mb-1 block">بحث</label>
                <Input 
                    id="search-input"
                    placeholder="بحث بالوصل أو رقم الهاتف..." 
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    disabled={!selectedAgentId}
                />
              </div>
            </CardContent>
          </Card>

          {selectedAgentId && selectedRows.size > 0 && (
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-lg">ملخص المحاسبة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex justify-between"><span>إجمالي مبلغ الطلبات المحددة:</span> <span className="font-semibold">{formatCurrency(totalSelectedAmount)}</span></div>
                <div className="flex justify-between"><span>عدد الطلبات المحددة:</span> <span className="font-semibold">{selectedRows.size}</span></div>
                <div className="flex justify-between"><span>عمولة المندوب ({formatCurrency(commissionPerOrder)} لكل طلب):</span> <span className="font-semibold text-red-600">-{formatCurrency(totalCommission)}</span></div>
                <hr className="my-2"/>
                <div className="flex justify-between text-base"><strong>الصافي المستحق للمندوب:</strong> <strong className="text-green-600">{formatCurrency(netPayable)}</strong></div>
              </CardContent>
               <CardDescription className="p-4 text-xs text-muted-foreground border-t">
                  تأكد من صحة البيانات قبل تأكيد وحفظ المحاسبة. هذا الإجراء سيقوم بأرشفة الطلبات المحددة.
              </CardDescription>
            </Card>
          )}
        </div>

        <div className="lg:col-span-2">
          <Card className="shadow-lg">
            <CardHeader>
              <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
                <div>
                  <CardTitle>الطلبات المستحقة للمحاسبة</CardTitle>
                  <CardDescription>
                    {selectedAgentId ? `طلبات المندوب: ${availableAgents.find(a => a.id === selectedAgentId)?.name}` : "اختر مندوبًا لعرض الطلبات"}
                  </CardDescription>
                </div>
                 <div className="flex gap-2">
                    <Button variant="outline" onClick={handlePrintStatement} disabled={selectedRows.size === 0 || !selectedAgentId}><Printer className="ml-2 h-4 w-4" /> طباعة</Button>
                    <Button onClick={handleConfirmAccounting} disabled={selectedRows.size === 0 || !selectedAgentId}><DollarSign className="ml-2 h-4 w-4" /> تأكيد وحفظ</Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {!selectedAgentId ? (
                <div className="text-center py-20 text-muted-foreground">
                  <Users className="mx-auto h-12 w-12 mb-4" />
                  <p>يرجى اختيار مندوب لعرض طلباته المستحقة للمحاسبة.</p>
                </div>
              ) : isFetchingOrders ? (
                <div className="flex justify-center items-center h-48">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="mr-4">جار تحميل طلبات المندوب...</p>
                </div>
              ) : (
                <div className="overflow-x-auto rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">
                          <Checkbox 
                              aria-label="Select all table orders" 
                              checked={displayedOrders.length > 0 && selectedRows.size === displayedOrders.length}
                              onCheckedChange={handleSelectAll}
                              disabled={displayedOrders.length === 0}
                          />
                        </TableHead>
                        <TableHead>رقم الوصل</TableHead>
                        <TableHead>اسم الشركة</TableHead>
                        <TableHead>المبلغ</TableHead>
                        <TableHead>الحالة</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {displayedOrders.length === 0 ? (
                          <TableRow><TableCell colSpan={5} className="text-center text-muted-foreground py-10">لا توجد طلبات محاسبة لعرضها حاليًا لهذا المندوب.</TableCell></TableRow>
                      ) : (
                        displayedOrders.map((order) => (
                          <TableRow key={order.id} data-state={selectedRows.has(order.id) ? "selected" : ""}>
                            <TableCell>
                                <Checkbox 
                                    aria-label={`Select order ${order.orderNumber}`} 
                                    checked={selectedRows.has(order.id)}
                                    onCheckedChange={() => handleRowSelect(order.id)}
                                />
                            </TableCell>
                            <TableCell className="font-medium">{order.orderNumber}</TableCell>
                            <TableCell>{order.companyName}</TableCell>
                            <TableCell>{formatCurrency(order.updatedAmount ?? order.amount)}</TableCell>
                            <TableCell><Badge variant={order.status === "تم التسليم" ? "default" : "secondary"}>{order.status}</Badge></TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

    {isStatementDialogOpen && selectedAgentId && (
      <Dialog open={isStatementDialogOpen} onOpenChange={setIsStatementDialogOpen}>
        <DialogContent className="sm:max-w-2xl receipt-dialog-content"> 
          <DialogHeader className="no-print">
            <DialogTitle>كشف محاسبة المندوب: {availableAgents.find(a=>a.id === selectedAgentId)?.name}</DialogTitle>
            <DialogDescription>
              تاريخ الطباعة: {new Date().toLocaleString('ar-IQ')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-3 text-sm printable-content"> 
            <div className="text-center mb-4">
                <FileText className="h-10 w-10 mx-auto text-primary mb-2"/>
                <h3 className="text-lg font-semibold">كشف محاسبة المندوب</h3>
                <p className="text-sm">{availableAgents.find(a=>a.id === selectedAgentId)?.name}</p>
                <p className="text-xs text-muted-foreground">تاريخ الكشف: {new Date().toLocaleDateString('ar-IQ', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
            </div>
            <h4 className="text-md font-semibold mt-3 mb-1 border-b pb-1">الطلبات المحاسب عليها:</h4>
            {selectedOrdersData.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>رقم الوصل</TableHead>
                    <TableHead>الشركة</TableHead>
                    <TableHead>المبلغ (د.ع)</TableHead>
                    <TableHead>الحالة</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {selectedOrdersData.map(order => (
                    <TableRow key={order.id}>
                      <TableCell>{order.orderNumber}</TableCell>
                      <TableCell>{order.companyName}</TableCell>
                      <TableCell>{formatCurrency(order.updatedAmount ?? order.amount)}</TableCell>
                      <TableCell>{order.status}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p>لم يتم تحديد طلبات.</p>
            )}
            <div className="mt-4 pt-4 border-t">
              <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                <p>إجمالي مبلغ الطلبات:</p><p className="font-semibold text-left">{formatCurrency(totalSelectedAmount)}</p>
                <p>عدد الطلبات:</p><p className="font-semibold text-left">{selectedOrdersData.length}</p>
                <p>إجمالي عمولة المندوب:</p><p className="font-semibold text-left text-red-600">-{formatCurrency(totalCommission)}</p>
              </div>
              <hr className="my-2"/>
              <div className="flex justify-between text-lg font-bold mt-2 bg-muted/50 p-3 rounded-md">
                <span>الصافي المستحق للمندوب:</span>
                <span className="text-green-600">{formatCurrency(netPayable)}</span>
              </div>
            </div>
             <p className="text-xs text-muted-foreground mt-6 text-center">هذا الكشف تم إنشاؤه بواسطة نظام رحلة للتوصيل.</p>
          </div>
          <DialogFooter className="no-print">
            <Button variant="secondary" onClick={() => setIsStatementDialogOpen(false)}>إغلاق</Button>
            <Button onClick={triggerSystemPrint}>
              <Printer className="ml-2 h-4 w-4" /> طباعة الكشف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )}
    </div>
  );
}
