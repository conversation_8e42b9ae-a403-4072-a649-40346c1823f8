"use client";

import React from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { History, Archive as ArchiveIcon, ArrowRight, Home } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

const archiveSections = [
  {
    title: "أرشيف الطلبات",
    description: "عرض وبحث جميع الطلبات التي تم أرشفتها بعد اكتمال دورتها.",
    href: "/archive/orders",
    icon: ArchiveIcon,
    iconColor: "text-blue-500"
  },
  {
    title: "أرشيف المحاسبات",
    description: "عرض سجلات المحاسبة والتسويات المالية المؤرشفة للمندوبين والعملاء.",
    href: "/accounting/archive",
    icon: History,
    iconColor: "text-green-500"
  },
];

export default function ArchiveHubPage() {
  const router = useRouter();
  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="مركز الأرشيف"
        description="اختر نوع الأرشيف الذي ترغب في استعراضه."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowRight className="h-4 w-4" />
              رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {archiveSections.map((section) => (
          <Link href={section.href} key={section.href} passHref>
            <Card className="shadow-lg hover:shadow-xl transition-shadow h-full flex flex-col items-center justify-center text-center p-8 cursor-pointer border-2 border-primary">
              <section.icon className={`h-16 w-16 ${section.iconColor} mb-4`} />
              <CardTitle className="text-xl font-bold">{section.title}</CardTitle>
              <CardDescription className="mt-2">{section.description}</CardDescription>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
