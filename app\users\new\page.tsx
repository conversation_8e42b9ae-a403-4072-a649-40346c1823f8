"use client";

import React, { useState, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowRight, UserPlus, Home, ShieldCheck, AlertTriangle, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import type { User, UserRole } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/auth-context";

// Mock data for demo
const iraqGovernorates = [
  { name: "بغداد", districts: [{ name: "الكرخ" }, { name: "الرصافة" }] },
  { name: "البصرة", districts: [{ name: "البصرة" }, { name: "الزبير" }] },
  { name: "أربيل", districts: [{ name: "أربيل" }, { name: "سوران" }] },
];

const getRolesForCreator = (role: UserRole): UserRole[] => {
  switch (role) {
    case "admin":
      return ["manager", "employee", "مندوب", "عميل", "متابع"];
    case "manager":
      return ["employee", "مندوب", "عميل", "متابع"];
    case "employee":
      return ["مندوب"];
    default:
      return [];
  }
};

const getDefaultPermissionsForRole = (role: UserRole | "") => {
  return {}; // Simplified for demo
};

export default function NewUserPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAuth();
  
  const [selectedGovernorate, setSelectedGovernorate] = useState<any>(null);
  const [selectedDistrictName, setSelectedDistrictName] = useState<string>("");
  const [availableRoles, setAvailableRoles] = useState<UserRole[]>([]);
  const [selectedRole, setSelectedRole] = useState<UserRole | "">("");
  const [permissions, setPermissions] = useState<any>(getDefaultPermissionsForRole("")); 
  const [canCreateUsers, setCanCreateUsers] = useState(false);

  useEffect(() => {
    if (currentUser) {
      const creatableRoles = getRolesForCreator(currentUser.role);
      setAvailableRoles(creatableRoles);
      const canCreate = creatableRoles.length > 0;
      setCanCreateUsers(canCreate);
      if (!canCreate) {
        toast({
          variant: "destructive",
          title: "غير مصرح",
          description: "ليس لديك الصلاحية لإضافة موظفين جدد.",
        });
      }
    }
  }, [currentUser, toast]);

  const handleRoleChange = (roleValue: string) => {
    const role = roleValue as UserRole;
    setSelectedRole(role);
    setPermissions(getDefaultPermissionsForRole(role));
  };

  const handleGovernorateChange = (governorateName: string) => {
    const gov = iraqGovernorates.find(g => g.name === governorateName);
    setSelectedGovernorate(gov || null);
    setSelectedDistrictName(""); 
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!currentUser) {
       toast({ variant: "destructive", title: "خطأ", description: "يجب تسجيل الدخول أولاً." });
       return;
    }
    if (!canCreateUsers) {
       toast({ variant: "destructive", title: "خطأ", description: "ليس لديك الصلاحية لإنشاء مستخدمين." });
      return;
    }
    
    const formData = new FormData(event.currentTarget);
    
    if (!selectedRole) {
      toast({ variant: "destructive", title: "خطأ", description: "يرجى اختيار دور للموظف." });
      return;
    }
    
    // Mock user creation
    toast({
      title: "تم الحفظ بنجاح",
      description: `تمت إضافة الموظف بنجاح.`,
    });
    router.push("/users"); 
  };
  
  if (!currentUser) {
    return (
        <div className="container mx-auto p-4 md:p-6 lg:p-8">
             <PageHeader
                title="إضافة موظف جديد"
                description="أدخل بيانات الموظف الجديد وحدد صلاحياته."
                 actions={
                  <div className="flex flex-wrap gap-2 items-center">
                    <Button variant="outline" onClick={() => router.back()}>
                      <ArrowRight className="h-4 w-4" />
                      رجوع
                    </Button>
                    <Button asChild variant="outline">
                      <Link href="/">
                        <Home className="h-4 w-4" />
                        الرئيسية
                      </Link>
                    </Button>
                  </div>
                }
              />
            <Card className="max-w-2xl mx-auto shadow-lg flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary mr-3" />
                <p>جار تحميل بيانات المستخدم...</p>
            </Card>
        </div>
    );
  }

  if (!canCreateUsers) { 
    return (
        <div className="container mx-auto p-4 md:p-6 lg:p-8">
             <PageHeader
                title="إضافة موظف جديد"
                description="أدخل بيانات الموظف الجديد وحدد صلاحياته."
                 actions={
                  <div className="flex flex-wrap gap-2 items-center">
                    <Button variant="outline" onClick={() => router.back()}>
                      <ArrowRight className="h-4 w-4" />
                      رجوع
                    </Button>
                    <Button asChild variant="outline">
                      <Link href="/">
                        <Home className="h-4 w-4" />
                        الرئيسية
                      </Link>
                    </Button>
                  </div>
                }
              />
            <Alert variant="destructive" className="max-w-2xl mx-auto">
                <AlertTriangle className="h-5 w-5" />
                <AlertTitle>غير مصرح!</AlertTitle>
                <AlertDescription>
                ليس لديك الصلاحية الكافية لإضافة موظفين جدد. يرجى مراجعة مسؤول النظام.
                </AlertDescription>
            </Alert>
        </div>
    );
  }
  
  const showPermissionsSection = selectedRole && selectedRole !== "مندوب" && selectedRole !== "عميل" && selectedRole !== "متابع";

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="إضافة موظف جديد"
        description="أدخل بيانات الموظف الجديد وحدد صلاحياته."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowRight className="h-4 w-4" />
              رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <Card className="max-w-2xl mx-auto shadow-lg">
        <CardHeader>
          <CardTitle>بيانات الموظف</CardTitle>
          <CardDescription>يرجى ملء جميع الحقول المطلوبة بعناية.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="fullName">الاسم الكامل</Label>
                <Input id="fullName" name="fullName" placeholder="مثال: أحمد علي محمد" required disabled={!canCreateUsers} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="username">اسم المستخدم (للدخول)</Label>
                <Input id="username" name="username" placeholder="مثال: user123" required disabled={!canCreateUsers} />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
               <div className="space-y-2">
                <Label htmlFor="password">كلمة المرور</Label>
                <Input id="password" name="password" type="password" placeholder="********" required disabled={!canCreateUsers}/>
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">صفة الموظف (الدور)</Label>
                <Select name="role" required onValueChange={handleRoleChange} value={selectedRole} disabled={!canCreateUsers}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر دور الموظف" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map(role => (
                      <SelectItem key={role} value={role}>{role}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="governorate">المحافظة</Label>
                <Select name="governorate" required onValueChange={handleGovernorateChange} disabled={!canCreateUsers}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المحافظة" />
                  </SelectTrigger>
                  <SelectContent>
                    {iraqGovernorates.map(gov => (
                      <SelectItem key={gov.name} value={gov.name}>{gov.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="district">القضاء/الناحية</Label>
                <Select name="district" value={selectedDistrictName} onValueChange={setSelectedDistrictName} required disabled={!canCreateUsers || !selectedGovernorate || selectedGovernorate.districts.length === 0}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر القضاء/الناحية" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedGovernorate?.districts.map((dist: any) => (
                      <SelectItem key={dist.name} value={dist.name}>{dist.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <Button type="submit" className="px-6" disabled={!canCreateUsers || !selectedRole}>
                <UserPlus className="ml-2 h-4 w-4" />
                إضافة الموظف
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
