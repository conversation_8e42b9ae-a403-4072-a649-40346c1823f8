import { Timestamp } from 'firebase/firestore';

export interface UserPermissions {
  orders: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  dispatch: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  returns: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  accounting: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  archive: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  users: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  importExport: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  notifications: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  imageShare: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
  settings: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
  };
}

export interface FirebaseUser {
  id: string;
  name: string;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'employee' | 'مندوب';
  permissions: UserPermissions;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  lastLogin?: Timestamp;
  phoneNumber?: string;
  profileImage?: string;
  companyId?: string;
  deviceTokens?: string[]; // For push notifications
}

export interface UserProfile {
  displayName: string;
  email: string;
  phoneNumber?: string;
  profileImage?: string;
  language: 'ar' | 'en';
  notificationSettings: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

// Default permissions for different roles
export const defaultPermissions: Record<FirebaseUser['role'], UserPermissions> = {
  admin: {
    orders: { view: true, create: true, edit: true, delete: true },
    dispatch: { view: true, create: true, edit: true, delete: true },
    returns: { view: true, create: true, edit: true, delete: true },
    accounting: { view: true, create: true, edit: true, delete: true },
    archive: { view: true, create: true, edit: true, delete: true },
    users: { view: true, create: true, edit: true, delete: true },
    importExport: { view: true, create: true, edit: true, delete: true },
    notifications: { view: true, create: true, edit: true, delete: true },
    imageShare: { view: true, create: true, edit: true, delete: true },
    settings: { view: true, create: true, edit: true, delete: true },
  },
  manager: {
    orders: { view: true, create: true, edit: true, delete: false },
    dispatch: { view: true, create: true, edit: true, delete: false },
    returns: { view: true, create: true, edit: true, delete: false },
    accounting: { view: true, create: false, edit: false, delete: false },
    archive: { view: true, create: false, edit: false, delete: false },
    users: { view: true, create: false, edit: false, delete: false },
    importExport: { view: true, create: true, edit: false, delete: false },
    notifications: { view: true, create: true, edit: true, delete: false },
    imageShare: { view: true, create: true, edit: true, delete: false },
    settings: { view: true, create: false, edit: true, delete: false },
  },
  employee: {
    orders: { view: true, create: true, edit: true, delete: false },
    dispatch: { view: true, create: false, edit: false, delete: false },
    returns: { view: true, create: true, edit: true, delete: false },
    accounting: { view: false, create: false, edit: false, delete: false },
    archive: { view: true, create: false, edit: false, delete: false },
    users: { view: false, create: false, edit: false, delete: false },
    importExport: { view: false, create: false, edit: false, delete: false },
    notifications: { view: true, create: false, edit: false, delete: false },
    imageShare: { view: true, create: true, edit: true, delete: false },
    settings: { view: true, create: false, edit: true, delete: false },
  },
  مندوب: {
    orders: { view: true, create: false, edit: true, delete: false },
    dispatch: { view: true, create: false, edit: false, delete: false },
    returns: { view: true, create: true, edit: true, delete: false },
    accounting: { view: false, create: false, edit: false, delete: false },
    archive: { view: false, create: false, edit: false, delete: false },
    users: { view: false, create: false, edit: false, delete: false },
    importExport: { view: false, create: false, edit: false, delete: false },
    notifications: { view: true, create: false, edit: false, delete: false },
    imageShare: { view: true, create: true, edit: true, delete: false },
    settings: { view: true, create: false, edit: true, delete: false },
  },
};
