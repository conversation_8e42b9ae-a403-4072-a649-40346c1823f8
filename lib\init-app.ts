"use client";

import { DataSeeder } from '@/lib/services/data-seeder';

let isInitialized = false;

export async function initializeApp(): Promise<void> {
  if (isInitialized) {
    return;
  }

  try {
    console.log('Initializing Rihla Delivery App...');
    
    // Check if we're in development mode and seed data if needed
    if (process.env.NODE_ENV === 'development') {
      await DataSeeder.seedIfEmpty();
    }
    
    isInitialized = true;
    console.log('App initialization completed');
  } catch (error) {
    console.error('Error initializing app:', error);
    // Don't throw error to prevent app from breaking
    // The app should work with mock data as fallback
  }
}

// Auto-initialize when this module is imported
if (typeof window !== 'undefined') {
  // Only run in browser
  initializeApp();
}
