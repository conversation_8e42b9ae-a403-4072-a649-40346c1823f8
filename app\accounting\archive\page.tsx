"use client";

import React, { useState, useMemo, useEffect, useCallback } from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Filter, ArrowRight, Home, History, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import type { AccountingRecordRep, User } from "@/types";
import { getRepSettlements } from "@/lib/order-data"; 
import { getUsers } from "@/lib/user-data"; 
import { useAuth } from "@/contexts/auth-context";

export default function AccountingArchivePage() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAuth();

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedEntityId, setSelectedEntityId] = useState("all");
  const [settlements, setSettlements] = useState<AccountingRecordRep[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    if (!currentUser) {
        setIsLoading(false);
        return;
    };
    
    try {
        const [repSettlementsData, usersData] = await Promise.all([
            getRepSettlements(),
            getUsers({ role: 'مندوب' }),
        ]);

        let allSettlements = repSettlementsData;

        if (currentUser.role !== 'admin') {
            allSettlements = allSettlements.filter(s => s.creatorId === currentUser.id);
        }
        
        setAllUsers(usersData);
        setSettlements(allSettlements.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
    } catch (error) {
        toast({variant: "destructive", title: "خطأ", description: "فشل تحميل بيانات الأرشيف."});
        console.error("Archive fetch error:", error);
    } finally {
        setIsLoading(false);
    }
  }, [currentUser, toast]);

  useEffect(() => {
    fetchData(); 
  }, [fetchData]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-IQ', { style: 'currency', currency: 'IQD', minimumFractionDigits: 0 }).format(amount);
  };

  const entityOptions = useMemo(() => {
    const relevantReps = [...new Set(settlements.map(s => s.representativeId))];
    return [{ id: "all", name: "جميع المندوبين" }, ...allUsers.filter(u => relevantReps.includes(u.id)).map(u => ({ id: u.id, name: u.name || u.username || u.id }))];
  }, [settlements, allUsers]);

  const filteredSettlements = useMemo(() => {
    return settlements.filter(record => {
      const matchesSearch = searchTerm === "" ||
                            record.settlementId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            record.representativeName.toLowerCase().includes(searchTerm.toLowerCase());
      
      let matchesEntity = true;
      if (selectedEntityId !== "all") {
        matchesEntity = record.representativeId === selectedEntityId;
      }

      return matchesSearch && matchesEntity;
    });
  }, [settlements, searchTerm, selectedEntityId]);
  
  const handleCardClick = (record: AccountingRecordRep) => {
    router.push(`/accounting/agents/${record.settlementId}`);
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="أرشيف المحاسبات المالية"
        description="عرض سجلات تسويات المندوبين المؤرشفة."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.push('/accounting')}>
              <ArrowRight className="h-4 w-4" />
              رجوع إلى المحاسبة
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><History className="h-6 w-6 text-primary"/> قائمة التسويات المؤرشفة</CardTitle>
          <CardDescription>يمكنك البحث أو استعراض تفاصيل التسويات المالية المؤرشفة.</CardDescription>
          <div className="flex flex-col md:flex-row gap-4 pt-4">
            <Input 
              placeholder="بحث برقم التسوية أو اسم المندوب..." 
              className="flex-1"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={selectedEntityId} onValueChange={setSelectedEntityId} >
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="اختر المندوب" />
              </SelectTrigger>
              <SelectContent>
                {entityOptions.map(option => (
                  <SelectItem key={option.id} value={option.id}>{option.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-10 text-muted-foreground">
                <Loader2 className="mx-auto h-12 w-12 animate-spin mb-4" />
                <p className="text-lg font-semibold">جار تحميل الأرشيف...</p>
            </div>
          ) : filteredSettlements.length === 0 ? (
            <div className="text-center py-10 text-muted-foreground">
              <Filter className="mx-auto h-12 w-12 mb-4" />
              <p className="text-lg font-semibold">لا توجد تسويات مؤرشفة تطابق معايير البحث.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredSettlements.map((record) => (
                <Card key={record.settlementId} className="shadow-md hover:shadow-lg transition-shadow cursor-pointer border-2 border-primary" onClick={() => handleCardClick(record)}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-semibold">{record.settlementId}</CardTitle>
                    <CardDescription>المندوب: {record.representativeName}</CardDescription>
                  </CardHeader>
                  <CardContent className="text-sm space-y-1 pt-0 pb-3">
                    <p><strong>المبلغ الإجمالي:</strong> {formatCurrency(record.totalCollectedAmount)}</p>
                    <p><strong>الصافي:</strong> {formatCurrency(record.netPayableToRep)}</p>
                    <p><strong>تاريخ التسوية:</strong> {new Date(record.date).toLocaleDateString('ar-IQ')}</p>
                  </CardContent>
                  <CardFooter className="pt-2 pb-3 px-4 border-t">
                     <Badge variant={record.paymentStatus === 'paid' ? 'default' : 'destructive'}>
                       {record.paymentStatus === 'paid' ? 'مدفوع' : 'معلق'}
                     </Badge>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
