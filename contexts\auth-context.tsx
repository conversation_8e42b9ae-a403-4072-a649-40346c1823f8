"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, createUser } from '@/lib/permissions';

interface AuthContextType {
  currentUser: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users for demo purposes
const mockUsers: User[] = [
  createUser({
    name: 'أحمد محمد',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
  }),
  createUser({
    name: 'فاطمة علي',
    email: '<EMAIL>',
    role: 'manager',
    isActive: true,
  }),
  createUser({
    name: 'محمد سالم',
    email: '<EMAIL>',
    role: 'employee',
    isActive: true,
  }),
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored user session
    const storedUser = localStorage.getItem('rihla_user');
    if (storedUser) {
      try {
        const user = JSON.parse(storedUser);
        setCurrentUser(user);
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('rihla_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock authentication - in real app, this would be an API call
    const user = mockUsers.find(u => u.email === email && u.isActive);
    
    if (user && (password === 'password' || password === '123456')) {
      const updatedUser = { ...user, lastLogin: new Date() };
      setCurrentUser(updatedUser);
      localStorage.setItem('rihla_user', JSON.stringify(updatedUser));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const logout = () => {
    setCurrentUser(null);
    localStorage.removeItem('rihla_user');
  };

  const value = {
    currentUser,
    login,
    logout,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
