import type { User, UserPermissions } from "@/types";

// Default permissions for different roles
const getDefaultPermissions = (role: string): UserPermissions => {
  switch (role) {
    case 'مدير عام':
      return {
        orders: { view: true, create: true, edit: true, delete: true },
        users: { view: true, create: true, edit: true, delete: true },
        accounting: { view: true, edit: true },
        archive: { view: true },
        reports: { view: true }
      };
    case 'مشرف':
      return {
        orders: { view: true, create: true, edit: true, delete: true },
        users: { view: true, create: true, edit: true, delete: false },
        accounting: { view: true, edit: true },
        archive: { view: true },
        reports: { view: true }
      };
    case 'موظف':
      return {
        orders: { view: true, create: true, edit: true, delete: false },
        users: { view: true, create: false, edit: false, delete: false },
        accounting: { view: true, edit: false },
        archive: { view: true },
        reports: { view: true }
      };
    case 'مندوب':
      return {
        orders: { view: true, create: false, edit: true, delete: false },
        users: { view: false, create: false, edit: false, delete: false },
        accounting: { view: true, edit: false },
        archive: { view: false },
        reports: { view: false }
      };
    case 'محاسب':
      return {
        orders: { view: true, create: false, edit: false, delete: false },
        users: { view: true, create: false, edit: false, delete: false },
        accounting: { view: true, edit: true },
        archive: { view: true },
        reports: { view: true }
      };
    default:
      return {
        orders: { view: true, create: false, edit: false, delete: false },
        users: { view: false, create: false, edit: false, delete: false },
        accounting: { view: false, edit: false },
        archive: { view: false },
        reports: { view: false }
      };
  }
};

// Mock users data
const mockUsers: User[] = [
  {
    id: "admin-001",
    name: "المدير العام",
    username: "admin",
    email: "<EMAIL>",
    role: "مدير عام",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('مدير عام')
  },
  {
    id: "manager-001",
    name: "أحمد المشرف",
    username: "manager",
    email: "<EMAIL>",
    role: "مشرف",
    isActive: true,
    governorate: "بغداد",
    district: "الرصافة",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('مشرف')
  },
  {
    id: "employee-001",
    name: "سارة الموظفة",
    username: "employee",
    email: "<EMAIL>",
    role: "موظف",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('موظف')
  },
  {
    id: "rep-001",
    name: "أحمد علي",
    username: "rep1",
    email: "<EMAIL>",
    role: "مندوب",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('مندوب')
  },
  {
    id: "rep-002",
    name: "محمد حسن",
    username: "rep2",
    email: "<EMAIL>",
    role: "مندوب",
    isActive: true,
    governorate: "بغداد",
    district: "الرصافة",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('مندوب')
  },
  {
    id: "rep-003",
    name: "علي محمود",
    username: "rep3",
    email: "<EMAIL>",
    role: "مندوب",
    isActive: true,
    governorate: "البصرة",
    district: "المركز",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('مندوب')
  },
  {
    id: "accountant-001",
    name: "فاطمة المحاسبة",
    username: "accountant1",
    email: "<EMAIL>",
    role: "محاسب",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('محاسب')
  },
  {
    id: "default-user",
    name: "المستخدم الافتراضي",
    username: "default",
    email: "<EMAIL>",
    role: "موظف",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    permissions: getDefaultPermissions('موظف')
  }
];

export class MockAuth {
  private static readonly PASSWORD = "password";
  private static readonly DEFAULT_PASSWORD = "123456";

  static async signIn(usernameOrEmail: string, password: string): Promise<User | null> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Check for default user with different password
    if (usernameOrEmail === 'default' && password === this.DEFAULT_PASSWORD) {
      const user = mockUsers.find(u => u.username === 'default');
      if (user && user.isActive) {
        // Store in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('currentUser', JSON.stringify(user));
        }
        return user;
      }
    }
    
    // Check regular password
    if (password !== this.PASSWORD) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    const user = mockUsers.find(u => 
      (u.username === usernameOrEmail || u.email === usernameOrEmail) && u.isActive
    );
    
    if (!user) {
      throw new Error('لم يتم العثور على المستخدم');
    }

    // Store in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('currentUser', JSON.stringify(user));
    }
    return user;
  }

  static async signOut(): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('currentUser');
    }
  }

  static getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const storedUser = localStorage.getItem('currentUser');
      if (storedUser) {
        return JSON.parse(storedUser);
      }
    } catch (error) {
      console.error('Error parsing stored user:', error);
      localStorage.removeItem('currentUser');
    }
    return null;
  }

  static getAllUsers(): User[] {
    return mockUsers;
  }

  static getUserById(id: string): User | undefined {
    return mockUsers.find(u => u.id === id);
  }

  static getUserByUsername(username: string): User | undefined {
    return mockUsers.find(u => u.username === username);
  }
}

export default MockAuth;
