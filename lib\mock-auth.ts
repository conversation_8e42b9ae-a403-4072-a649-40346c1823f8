import type { User } from "@/types";

// Mock users for testing
export const mockUsers: User[] = [
  {
    id: "admin-001",
    name: "المدير العام",
    username: "admin",
    email: "<EMAIL>",
    role: "admin",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "manager-001", 
    name: "أحمد المشرف",
    username: "manager",
    email: "<EMAIL>",
    role: "manager",
    isActive: true,
    governorate: "بغداد",
    district: "الرصافة",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "employee-001",
    name: "محمد الموظف",
    username: "employee", 
    email: "<EMAIL>",
    role: "employee",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "rep-001",
    name: "أحمد علي",
    username: "rep1",
    email: "<EMAIL>", 
    role: "مندوب",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "rep-002",
    name: "محمد حسن",
    username: "rep2",
    email: "<EMAIL>",
    role: "مندوب", 
    isActive: true,
    governorate: "بغداد",
    district: "الرصافة",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "rep-003",
    name: "علي محمود",
    username: "rep3",
    email: "<EMAIL>",
    role: "مندوب",
    isActive: true,
    governorate: "البصرة", 
    district: "البصرة",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "client-001",
    name: "شركة النور",
    username: "client1",
    email: "<EMAIL>",
    role: "عميل",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "client-002", 
    name: "شركة الأمل",
    username: "client2",
    email: "<EMAIL>",
    role: "عميل",
    isActive: true,
    governorate: "بغداد",
    district: "الرصافة",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "follower-001",
    name: "سارة المتابعة",
    username: "follower1", 
    email: "<EMAIL>",
    role: "متابع",
    isActive: true,
    governorate: "بغداد",
    district: "الكرخ",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// Mock authentication functions
export class MockAuth {
  private static readonly STORAGE_KEY = 'rihla_current_user';
  private static readonly PASSWORD = 'password'; // Same password for all users

  static async signIn(usernameOrEmail: string, password: string): Promise<User | null> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    if (password !== this.PASSWORD) {
      throw new Error('كلمة المرور غير صحيحة');
    }

    const user = mockUsers.find(u => u.username === usernameOrEmail || u.email === usernameOrEmail);
    if (!user) {
      throw new Error('لم يتم العثور على المستخدم');
    }

    if (!user.isActive) {
      throw new Error('تم تعطيل هذا الحساب');
    }

    // Store user in localStorage
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(user));
    
    return user;
  }

  static async signOut(): Promise<void> {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  static getCurrentUser(): User | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error getting current user:', error);
    }
    return null;
  }

  static isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }

  static async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    const currentUser = this.getCurrentUser();
    if (currentUser && currentUser.id === userId) {
      const updatedUser = { ...currentUser, ...updates, updatedAt: new Date().toISOString() };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedUser));
      return updatedUser;
    }
    return null;
  }

  // Get all users (for admin/manager functions)
  static getAllUsers(): User[] {
    return mockUsers;
  }

  // Add new user (for admin/manager functions)
  static async addUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const newUser: User = {
      ...userData,
      id: `user-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    mockUsers.push(newUser);
    return newUser;
  }

  // Delete user
  static async deleteUser(userId: string): Promise<boolean> {
    const index = mockUsers.findIndex(u => u.id === userId);
    if (index > -1) {
      mockUsers.splice(index, 1);
      return true;
    }
    return false;
  }

  // Update user in mock data
  static async updateUserInMockData(userId: string, updates: Partial<User>): Promise<User | null> {
    const userIndex = mockUsers.findIndex(u => u.id === userId);
    if (userIndex > -1) {
      mockUsers[userIndex] = { 
        ...mockUsers[userIndex], 
        ...updates, 
        updatedAt: new Date().toISOString() 
      };
      
      // Update current user if it's the same user
      const currentUser = this.getCurrentUser();
      if (currentUser && currentUser.id === userId) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mockUsers[userIndex]));
      }
      
      return mockUsers[userIndex];
    }
    return null;
  }
}
