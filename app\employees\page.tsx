"use client";

import React, { useState, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { 
  Home, 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  MoreHorizontal,
  Phone,
  Mail,
  MapPin
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { useToast } from "@/hooks/use-toast";
import type { User, UserRole } from "@/types";
import { getUsers, createUser, updateUser, deleteUserById } from "@/lib/user-data";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export default function EmployeesPage() {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  
  const [employees, setEmployees] = useState<User[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<User | null>(null);
  
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    email: "",
    role: "موظف" as UserRole,
    phoneNumber: "",
    governorate: "",
    district: "",
    isActive: true
  });

  useEffect(() => {
    fetchEmployees();
  }, []);

  useEffect(() => {
    filterEmployees();
  }, [employees, searchTerm, roleFilter, statusFilter]);

  const fetchEmployees = async () => {
    try {
      setIsLoading(true);
      const data = await getUsers();
      setEmployees(data);
    } catch (error) {
      console.error("Error fetching employees:", error);
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "فشل تحميل بيانات الموظفين"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterEmployees = () => {
    let filtered = employees;

    if (searchTerm) {
      filtered = filtered.filter(emp => 
        emp.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.phoneNumber?.includes(searchTerm)
      );
    }

    if (roleFilter !== "all") {
      filtered = filtered.filter(emp => emp.role === roleFilter);
    }

    if (statusFilter !== "all") {
      const isActive = statusFilter === "active";
      filtered = filtered.filter(emp => emp.isActive === isActive);
    }

    setFilteredEmployees(filtered);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingEmployee) {
        await updateUser(editingEmployee.id, formData);
        toast({
          title: "تم التحديث",
          description: "تم تحديث بيانات الموظف بنجاح"
        });
      } else {
        await createUser(formData);
        toast({
          title: "تم الإضافة",
          description: "تم إضافة الموظف الجديد بنجاح"
        });
      }
      
      setIsDialogOpen(false);
      resetForm();
      fetchEmployees();
      
    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: editingEmployee ? "فشل تحديث الموظف" : "فشل إضافة الموظف"
      });
    }
  };

  const handleEdit = (employee: User) => {
    setEditingEmployee(employee);
    setFormData({
      name: employee.name || "",
      username: employee.username,
      email: employee.email,
      role: employee.role,
      phoneNumber: employee.phoneNumber || "",
      governorate: employee.governorate || "",
      district: employee.district || "",
      isActive: employee.isActive
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (employeeId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا الموظف؟")) return;
    
    try {
      await deleteUserById(employeeId);
      toast({
        title: "تم الحذف",
        description: "تم حذف الموظف بنجاح"
      });
      fetchEmployees();
    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "فشل حذف الموظف"
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      username: "",
      email: "",
      role: "موظف",
      phoneNumber: "",
      governorate: "",
      district: "",
      isActive: true
    });
    setEditingEmployee(null);
  };

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'مدير عام': return 'default';
      case 'مشرف': return 'secondary';
      case 'موظف': return 'outline';
      case 'مندوب': return 'destructive';
      case 'محاسب': return 'secondary';
      default: return 'outline';
    }
  };

  if (!currentUser?.permissions?.users?.view) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="text-center py-10">
            <p className="text-lg text-muted-foreground">
              ليس لديك صلاحية لعرض الموظفين
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <PageHeader
        title="إدارة الموظفين"
        description="عرض وإدارة جميع الموظفين في النظام"
        actions={
          <div className="flex gap-2">
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
            {currentUser?.permissions?.users?.create && (
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={resetForm}>
                    <UserPlus className="h-4 w-4" />
                    إضافة موظف جديد
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>
                      {editingEmployee ? 'تعديل الموظف' : 'إضافة موظف جديد'}
                    </DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">الاسم الكامل</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="username">اسم المستخدم</Label>
                      <Input
                        id="username"
                        value={formData.username}
                        onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="role">الدور</Label>
                      <Select value={formData.role} onValueChange={(value: UserRole) => setFormData(prev => ({ ...prev, role: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="مدير عام">مدير عام</SelectItem>
                          <SelectItem value="مشرف">مشرف</SelectItem>
                          <SelectItem value="موظف">موظف</SelectItem>
                          <SelectItem value="مندوب">مندوب</SelectItem>
                          <SelectItem value="محاسب">محاسب</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phoneNumber}
                        onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="governorate">المحافظة</Label>
                        <Select value={formData.governorate} onValueChange={(value) => setFormData(prev => ({ ...prev, governorate: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر المحافظة" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="بغداد">بغداد</SelectItem>
                            <SelectItem value="البصرة">البصرة</SelectItem>
                            <SelectItem value="أربيل">أربيل</SelectItem>
                            <SelectItem value="النجف">النجف</SelectItem>
                            <SelectItem value="كربلاء">كربلاء</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="district">المنطقة</Label>
                        <Input
                          id="district"
                          value={formData.district}
                          onChange={(e) => setFormData(prev => ({ ...prev, district: e.target.value }))}
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="active"
                        checked={formData.isActive}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                      />
                      <Label htmlFor="active">نشط</Label>
                    </div>
                    
                    <div className="flex justify-end gap-2">
                      <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                        إلغاء
                      </Button>
                      <Button type="submit">
                        {editingEmployee ? 'تحديث' : 'إضافة'}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            )}
          </div>
        }
      />

      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              البحث والفلترة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="بحث بالاسم، اسم المستخدم، البريد..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="فلترة حسب الدور" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأدوار</SelectItem>
                  <SelectItem value="مدير عام">مدير عام</SelectItem>
                  <SelectItem value="مشرف">مشرف</SelectItem>
                  <SelectItem value="موظف">موظف</SelectItem>
                  <SelectItem value="مندوب">مندوب</SelectItem>
                  <SelectItem value="محاسب">محاسب</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="inactive">غير نشط</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  النتائج: {filteredEmployees.length} من {employees.length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Employees Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              قائمة الموظفين ({filteredEmployees.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-10">
                <p>جار التحميل...</p>
              </div>
            ) : filteredEmployees.length === 0 ? (
              <div className="text-center py-10 text-muted-foreground">
                <Users className="mx-auto h-12 w-12 mb-4" />
                <p className="text-lg font-semibold">لا توجد موظفين</p>
                <p>لم يتم العثور على موظفين يطابقون معايير البحث</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الاسم</TableHead>
                      <TableHead>اسم المستخدم</TableHead>
                      <TableHead>الدور</TableHead>
                      <TableHead>المحافظة</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>تاريخ الإنشاء</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEmployees.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{employee.name}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {employee.email}
                            </div>
                            {employee.phoneNumber && (
                              <div className="text-sm text-muted-foreground flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                {employee.phoneNumber}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{employee.username}</TableCell>
                        <TableCell>
                          <Badge variant={getRoleBadgeVariant(employee.role)}>
                            {employee.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {employee.governorate}
                            {employee.district && ` - ${employee.district}`}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={employee.isActive ? "default" : "secondary"}>
                            {employee.isActive ? "نشط" : "غير نشط"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(employee.createdAt).toLocaleDateString('ar-SA')}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEdit(employee)}>
                                <Edit className="h-4 w-4 mr-2" />
                                تعديل
                              </DropdownMenuItem>
                              {currentUser?.permissions?.users?.delete && (
                                <DropdownMenuItem 
                                  onClick={() => handleDelete(employee.id)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  حذف
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
