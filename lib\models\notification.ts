import { Timestamp } from 'firebase/firestore';

export type NotificationType = 
  | 'order_created'
  | 'order_updated'
  | 'order_assigned'
  | 'order_delivered'
  | 'order_returned'
  | 'order_cancelled'
  | 'payment_received'
  | 'system_update'
  | 'user_message'
  | 'reminder';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface FirebaseNotification {
  id: string;
  
  // Recipients
  userId: string;
  companyId?: string;
  
  // Content
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  
  // Status
  isRead: boolean;
  isArchived: boolean;
  
  // Timestamps
  createdAt: Timestamp;
  readAt?: Timestamp;
  expiresAt?: Timestamp;
  
  // Related Data
  relatedOrderId?: string;
  relatedUserId?: string;
  relatedCompanyId?: string;
  
  // Action
  actionUrl?: string;
  actionLabel?: string;
  
  // Metadata
  metadata?: Record<string, any>;
  
  // Delivery channels
  channels: {
    inApp: boolean;
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  
  // Delivery status
  deliveryStatus: {
    inApp: 'pending' | 'delivered' | 'failed';
    email: 'pending' | 'delivered' | 'failed' | 'not_sent';
    sms: 'pending' | 'delivered' | 'failed' | 'not_sent';
    push: 'pending' | 'delivered' | 'failed' | 'not_sent';
  };
}

export interface NotificationTemplate {
  id: string;
  type: NotificationType;
  name: string;
  
  // Template content
  titleTemplate: string;
  messageTemplate: string;
  
  // Default settings
  defaultPriority: NotificationPriority;
  defaultChannels: {
    inApp: boolean;
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  
  // Variables that can be used in templates
  availableVariables: string[];
  
  // Conditions
  isActive: boolean;
  companyId?: string; // If null, it's a system template
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface NotificationSettings {
  id: string;
  userId: string;
  
  // Channel preferences
  channels: {
    inApp: boolean;
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  
  // Type preferences
  typePreferences: Record<NotificationType, {
    enabled: boolean;
    channels: {
      inApp: boolean;
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  }>;
  
  // Quiet hours
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
    timezone: string;
  };
  
  // Frequency limits
  frequencyLimits: {
    maxPerHour: number;
    maxPerDay: number;
  };
  
  updatedAt: Timestamp;
}

export interface PushToken {
  id: string;
  userId: string;
  token: string;
  platform: 'web' | 'android' | 'ios';
  deviceInfo?: {
    userAgent?: string;
    deviceModel?: string;
    osVersion?: string;
  };
  isActive: boolean;
  createdAt: Timestamp;
  lastUsed: Timestamp;
}

export const NOTIFICATION_TYPES = [
  { key: 'order_created', label: 'طلب جديد', icon: 'plus' },
  { key: 'order_updated', label: 'تحديث طلب', icon: 'edit' },
  { key: 'order_assigned', label: 'إسناد طلب', icon: 'user-plus' },
  { key: 'order_delivered', label: 'تم التسليم', icon: 'check' },
  { key: 'order_returned', label: 'طلب مرتجع', icon: 'arrow-left' },
  { key: 'order_cancelled', label: 'طلب ملغي', icon: 'x' },
  { key: 'payment_received', label: 'دفعة مستلمة', icon: 'dollar-sign' },
  { key: 'system_update', label: 'تحديث النظام', icon: 'settings' },
  { key: 'user_message', label: 'رسالة مستخدم', icon: 'message-circle' },
  { key: 'reminder', label: 'تذكير', icon: 'bell' },
];

export const NOTIFICATION_PRIORITIES = [
  { key: 'low', label: 'منخفض', color: 'gray' },
  { key: 'normal', label: 'عادي', color: 'blue' },
  { key: 'high', label: 'عالي', color: 'orange' },
  { key: 'urgent', label: 'عاجل', color: 'red' },
];
