"use client";

import React, { useState, useRef, useMemo, useEffect } from 'react';
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { UploadCloud, FileText, ArrowRight, Home, Loader2, Info, CheckCircle, Hourglass, Database, AlertTriangle } from "lucide-react";
import { getOrders } from "@/lib/order-data";
import type { Order } from "@/types";
import { useAuth } from '@/contexts/auth-context';
import { Label } from '@/components/ui/label';
import { useRouter } from 'next/navigation';
import Link from "next/link";
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

type ImportTemplateType = "almasarrah" | "albarq";
type ImportPhase = 'idle' | 'reading' | 'checking' | 'writing' | 'done';

const columnMapping: Record<ImportTemplateType, { orderNumber: number; companyName?: number | string; customerPhone: number; amount: number; address?: number }> = {
  almasarrah: { orderNumber: 2, companyName: 3, customerPhone: 7, amount: 8 },
  albarq: { orderNumber: 0, customerPhone: 5, amount: 7, address: 6, companyName: 'البرق' },
};

// Mock functions for demo
async function getOrdersByOrderNumbers(orderNumbers: string[]): Promise<Order[]> {
  const allOrders = await getOrders();
  return allOrders.filter(order => orderNumbers.includes(order.orderNumber));
}

async function addMultipleOrders(orders: Omit<Order, 'id'>[], progressCallback?: (processed: number, total: number) => void): Promise<void> {
  // Simulate adding orders with progress
  for (let i = 0; i < orders.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 100)); // Simulate API delay
    progressCallback?.(i + 1, orders.length);
  }
}

export default function ImportOrdersFromExcelPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { toast } = useToast();
  const { currentUser } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const [importTemplate, setImportTemplate] = useState<ImportTemplateType>("almasarrah");

  const [importPhase, setImportPhase] = useState<ImportPhase>('idle');
  const [progress, setProgress] = useState<{ processed: number, total: number } | null>(null);
  const [importStats, setImportStats] = useState<{ added: number, skipped: number } | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" || file.type === "application/vnd.ms-excel" || file.type === "text/csv") {
        setSelectedFile(file);
        setImportPhase('idle');
        setImportStats(null);
        setProgress(null);
      } else {
        toast({
          title: "نوع ملف غير مدعوم",
          description: "الرجاء اختيار ملف Excel بصيغة .xlsx أو .xls.",
          variant: "destructive",
        });
        if (fileInputRef.current) fileInputRef.current.value = "";
        setSelectedFile(null);
      }
    } else {
      setSelectedFile(null);
    }
  };

  const processData = async (dataRows: any[][]) => {
    if (!currentUser) return;
    
    const contentRows = dataRows.slice(1).filter(row => row.some(cell => cell !== null && cell !== ''));
    
    if (contentRows.length === 0) {
      toast({ title: "ملف فارغ", description: "الملف الذي تم تحميله لا يحتوي على أي بيانات صالحة.", variant: "destructive" });
      setImportPhase('idle');
      return;
    }

    setImportPhase('checking');
    const mapping = columnMapping[importTemplate];
    const orderNumberIndex = mapping.orderNumber;
    const phoneIndex = mapping.customerPhone;

    if (contentRows[0].length <= Math.max(orderNumberIndex, phoneIndex)) {
        toast({
            title: "خطأ في تنسيق الملف",
            description: "عدد الأعمدة في الملف أقل من المتوقع للنموذج المختار. يرجى التحقق من الملف.",
            variant: "destructive",
        });
        setImportPhase('idle');
        return;
    }
    
    const orderNumbersFromSheet = contentRows.map(row => String(row[orderNumberIndex] || '').trim()).filter(Boolean);
    if(orderNumbersFromSheet.length === 0) {
       toast({ title: "بيانات ناقصة", description: "لم يتم العثور على أرقام وصولات في العمود المخصص.", variant: "destructive" });
       setImportPhase('idle');
       return;
    }
    const existingOrders = await getOrdersByOrderNumbers(orderNumbersFromSheet);
    const existingOrderNumbersSet = new Set(existingOrders.map(o => o.orderNumber));
    
    const allOrdersToAdd: Omit<Order, 'id'>[] = [];
    let addedCount = 0;
    let skippedCount = 0;

    for (const row of contentRows) {
        const orderNumber = String(row[orderNumberIndex] || '').trim();
        const customerPhoneRaw = String(row[phoneIndex] || '').trim();
        
        if (!orderNumber || !customerPhoneRaw || existingOrderNumbersSet.has(orderNumber)) {
            skippedCount++;
        } else {
            let customerPhone = customerPhoneRaw;
            if (!customerPhone.startsWith('0') && customerPhone.length >= 9 && /^\d+$/.test(customerPhone)) {
                customerPhone = `0${customerPhone}`;
            }

            const companyName = typeof mapping.companyName === 'string'
              ? mapping.companyName
              : String(row[mapping.companyName!] || '').trim();

            const amountStr = String(row[mapping.amount] || '0').replace(/[^0-9.-]/g, '');
            const amount = parseFloat(amountStr) || 0;
            const address = mapping.address !== undefined ? String(row[mapping.address] || 'غير محدد').trim() : 'غير محدد';
            
            allOrdersToAdd.push({
                orderNumber,
                companyName,
                mobileNumber: customerPhone,
                address,
                amount,
                status: 'pending',
                createdAt: new Date(),
                updatedAt: new Date(),
            } as any);
            addedCount++;
        }
    }
    
    setImportStats({ added: addedCount, skipped: skippedCount });

    try {
        if (allOrdersToAdd.length > 0) {
          setImportPhase('writing');
          setProgress({ processed: 0, total: allOrdersToAdd.length });
          await addMultipleOrders(allOrdersToAdd, (processed, total) => {
            setProgress({ processed, total });
          });
        }
        setImportPhase('done');
        toast({ 
          title: "اكتمل الاستيراد بنجاح", 
          description: `تمت إضافة ${addedCount} طلبات جديدة. تم تخطي ${skippedCount} طلبات (مكررة أو بياناتها ناقصة).`,
          duration: 7000
        });
    } catch (error: any) {
        console.error("Error during database write:", error);
        setImportPhase('idle'); 
        setProgress(null);
        toast({
            variant: "destructive",
            title: "فشل الحفظ في قاعدة البيانات",
            description: `حدث خطأ أثناء محاولة حفظ الطلبات. قد يكون السبب مشكلة في الاتصال أو صلاحيات الكتابة.`,
            duration: 10000
        });
    }
  };

  const handleImport = async () => {
    if (!selectedFile || !currentUser) {
        toast({ title: "خطأ", description: !selectedFile ? "لم يتم اختيار ملف" : "يجب تسجيل الدخول أولاً.", variant: "destructive" });
        return;
    }
    
    setImportPhase('reading');
    setImportStats(null);
    setProgress(null);
    toast({ title: "بدء عملية الاستيراد...", description: "جاري قراءة الملف. قد تستغرق هذه العملية بعض الوقت للملفات الكبيرة." });
    
    // Simulate file reading
    setTimeout(() => {
      // Mock data for demo
      const mockData = [
        ['Header1', 'Header2', 'OrderNumber', 'Company', 'Header5', 'Header6', 'Header7', 'Phone', 'Amount'],
        ['', '', 'ORD-TEST-001', 'شركة المسرة', '', '', '', '0501234567', '150'],
        ['', '', 'ORD-TEST-002', 'شركة المسرة', '', '', '', '0507654321', '200'],
      ];
      processData(mockData);
    }, 1000);
  };
  
  const phaseDetails = {
    idle: { icon: UploadCloud, text: "بدء الاستيراد", color: "" },
    reading: { icon: Hourglass, text: "جاري قراءة الملف...", color: "text-amber-500" },
    checking: { icon: Hourglass, text: "جاري فحص التكرار...", color: "text-amber-500" },
    writing: { icon: Database, text: "جاري الحفظ في قاعدة البيانات...", color: "text-blue-500" },
    done: { icon: CheckCircle, text: "اكتمل الاستيراد", color: "text-green-500" }
  };
  const currentPhase = phaseDetails[importPhase];
  const isLoading = importPhase === 'reading' || importPhase === 'checking' || importPhase === 'writing';

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="استيراد الطلبات من ملف Excel"
        description="تسريع عملية إدخال الطلبات عبر تحميلها مباشرة من ملف Excel."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.push("/import-export")} disabled={isLoading}>
              <ArrowRight className="ml-2 h-4 w-4 rtl:mr-2 rtl:ml-0" /> رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" /> الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <div className="grid gap-6">
        <Card className="shadow-lg border-2 border-primary">
          <CardHeader>
            <CardTitle className="flex items-center gap-2"><FileText className="h-6 w-6 text-primary"/> تحميل ملف الطلبات</CardTitle>
            <CardDescription>حدد نموذج الاستيراد ثم اختر ملف Excel. سيتعرف النظام على الأعمدة بناءً على موقعها.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label className="block text-sm font-medium text-foreground mb-2">1. اختر نموذج الاستيراد</Label>
              <Select dir="rtl" value={importTemplate} onValueChange={(v) => setImportTemplate(v as ImportTemplateType)} disabled={isLoading}>
                <SelectTrigger><SelectValue placeholder="اختر نموذج الاستيراد"/></SelectTrigger>
                <SelectContent>
                  <SelectItem value="almasarrah">نموذج شركة المسرة</SelectItem>
                  <SelectItem value="albarq">نموذج شركة البرق</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Card className="p-3 border-dashed bg-muted/50">
              <CardHeader className="p-0 pb-2">
                <CardTitle className="text-base flex items-center gap-1"><Info className="h-4 w-4 text-primary"/>تنسيق الأعمدة المطلوب</CardTitle>
              </CardHeader>
              <CardContent className="p-0 text-sm text-muted-foreground">
                {importTemplate === 'almasarrah' ? (
                  <ul className="list-disc pr-4">
                    <li>رقم الوصل: العمود <code className="font-mono text-primary">C</code></li>
                    <li>اسم الشركة: العمود <code className="font-mono text-primary">D</code></li>
                    <li>هاتف المستلم: العمود <code className="font-mono text-primary">H</code></li>
                    <li>السعر: العمود <code className="font-mono text-primary">I</code></li>
                  </ul>
                ) : (
                  <ul className="list-disc pr-4">
                    <li>رقم الوصل: العمود <code className="font-mono text-primary">A</code></li>
                    <li>عنوان الزبون: العمود <code className="font-mono text-primary">G</code></li>
                    <li>هاتف المستلم: العمود <code className="font-mono text-primary">F</code></li>
                    <li>السعر: العمود <code className="font-mono text-primary">H</code></li>
                    <li>اسم الشركة: سيتم تعيينه تلقائيًا إلى "البرق".</li>
                  </ul>
                )}
              </CardContent>
            </Card>

            <div>
              <Label htmlFor="excel-file-input" className="block text-sm font-medium text-foreground mb-1">2. اختر ملف Excel</Label>
              <Input
                id="excel-file-input"
                type="file"
                accept=".xlsx, .xls, .csv"
                onChange={handleFileChange}
                ref={fileInputRef}
                className="block w-full text-sm text-muted-foreground file:ml-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                disabled={isLoading}
              />
              {selectedFile && !isLoading && (
                <p className="mt-2 text-sm text-muted-foreground">
                  الملف المختار: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
                </p>
              )}
            </div>
            
            {importPhase !== 'idle' && (
              <div className="mt-4 space-y-2">
                <div className={`flex items-center gap-2 font-semibold ${currentPhase.color}`}>
                    {isLoading ? <Loader2 className="h-4 w-4 animate-spin"/> : <currentPhase.icon className="h-4 w-4" />}
                    <Label>{currentPhase.text}</Label>
                </div>
                {progress && (importPhase === 'writing' || importPhase === 'checking') && (
                    <Progress value={(progress.processed / progress.total) * 100} />
                )}
                {importStats && (
                  <p className="text-sm text-muted-foreground text-center">
                    النتيجة: {importStats.added} طلبات جديدة | {importStats.skipped} طلبات تم تخطيها (مكررة أو ناقصة)
                  </p>
                )}
              </div>
            )}
            
            <Button onClick={handleImport} disabled={!selectedFile || isLoading} className="w-full sm:w-auto">
              {isLoading ? <Loader2 className="ml-2 h-4 w-4 animate-spin"/> : <UploadCloud className="ml-2 h-4 w-4 rtl:mr-2 rtl:ml-0" />}
              {isLoading ? "جاري الاستيراد..." : "بدء الاستيراد"}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
