"use client";

import React from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileUp, FileDown, Trash2, ArrowRight, Home } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

const importExportSections = [
  {
    title: "استيراد طلبات جديدة",
    description: "قم بتحميل ملف Excel يحتوي على الطلبات الجديدة.",
    href: "/import-export/new-orders",
    icon: FileUp,
    iconColor: "text-blue-500"
  },
  {
    title: "تصدير الطلبات",
    description: "اختر معايير لتصدير مجموعة من الطلبات إلى ملف Excel.",
    href: "/export-orders",
    icon: FileDown,
    iconColor: "text-green-500"
  },
  {
    title: "حذف الطلبات",
    description: "اختر معايير لحذف مجموعة من الطلبات بشكل نهائي.",
    href: "/delete-orders",
    icon: Trash2,
    iconColor: "text-red-500"
  },
];

export default function ImportExportHubPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="استيراد وتصدير وإدارة الطلبات بالجملة"
        description="اختر العملية التي ترغب بتنفيذها."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowRight className="h-4 w-4" />
              رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {importExportSections.map((section) => (
          <Link href={section.href} key={section.href} passHref>
            <Card className="shadow-lg hover:shadow-xl transition-shadow h-full flex flex-col items-center justify-center text-center p-6 cursor-pointer border-2 border-primary">
              <section.icon className={`h-12 w-12 ${section.iconColor} mb-3`} />
              <CardTitle>{section.title}</CardTitle>
              <CardDescription>{section.description}</CardDescription>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
