"use client";

import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Upload, Download, Trash2, ArrowR<PERSON>, Home } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function ImportExportPage() {
  const router = useRouter();

  return (
    <>
      <PageHeader 
        title="استيراد/تصدير/حذف" 
        description="عمليات مجمعة على الطلبات"
      >
        <div className="flex flex-wrap gap-2 items-center">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowRight className="h-4 w-4" />
            رجوع
          </Button>
          <Button asChild variant="outline">
            <Link href="/">
              <Home className="h-4 w-4" />
              الرئيسية
            </Link>
          </Button>
        </div>
      </PageHeader>
      
      <div className="container mx-auto px-4 py-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-6 w-6 text-blue-500" />
                استيراد الطلبات
              </CardTitle>
              <CardDescription>
                رفع ملف Excel أو CSV لإضافة طلبات جديدة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                <Upload className="h-4 w-4 ml-2" />
                اختر ملف للاستيراد
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-6 w-6 text-green-500" />
                تصدير الطلبات
              </CardTitle>
              <CardDescription>
                تحميل الطلبات كملف Excel أو CSV
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full">
                <Download className="h-4 w-4 ml-2" />
                تصدير جميع الطلبات
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trash2 className="h-6 w-6 text-red-500" />
                حذف الطلبات
              </CardTitle>
              <CardDescription>
                حذف مجموعة من الطلبات حسب معايير محددة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild variant="destructive" className="w-full">
                <Link href="/delete-orders">
                  <Trash2 className="h-4 w-4 ml-2" />
                  إدارة حذف الطلبات
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
