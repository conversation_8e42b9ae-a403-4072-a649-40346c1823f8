import { Timestamp } from 'firebase/firestore';

export type OrderStatus = 'pending' | 'confirmed' | 'dispatched' | 'delivered' | 'returned' | 'cancelled' | 'postponed';

export interface OrderItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  price: number;
  total: number;
  sku?: string;
}

export interface FirebaseOrder {
  id: string;
  orderNumber: string;
  
  // Company/Client Information
  companyId: string;
  companyName: string;
  clientPhoneNumber?: string;
  clientEmail?: string;
  
  // Customer Information (Recipient)
  customerName: string;
  mobileNumber: string;
  alternativePhone?: string;
  address: string;
  city: string;
  district?: string;
  landmark?: string;
  
  // Order Details
  items: OrderItem[];
  amount: number;
  updatedAmount?: number;
  currency: string;
  paymentMethod: 'cash' | 'card' | 'transfer' | 'wallet';
  
  // Status and Assignment
  status: OrderStatus;
  agentId?: string;
  agentName?: string;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  scheduledDeliveryDate?: Timestamp;
  deliveredAt?: Timestamp;
  
  // Additional Information
  notes?: string;
  internalNotes?: string;
  rejectedReason?: string;
  postponeReason?: string;
  partiallyReturnedItems?: number;
  
  // Tracking
  trackingNumber?: string;
  importedClientIdentifier?: string;
  
  // Location
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  
  // Images
  images?: string[];
  deliveryProofImages?: string[];
  
  // Delivery attempts
  deliveryAttempts: number;
  maxDeliveryAttempts: number;
  
  // Financial
  deliveryFee: number;
  codAmount?: number; // Cash on Delivery amount
  
  // Priority
  priority: 'low' | 'normal' | 'high' | 'urgent';
  
  // Tags
  tags?: string[];
}

export interface OrderStatusHistory {
  id: string;
  orderId: string;
  status: OrderStatus;
  changedBy: string;
  changedAt: Timestamp;
  notes?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
}

export interface DeliveryAttempt {
  id: string;
  orderId: string;
  agentId: string;
  attemptedAt: Timestamp;
  status: 'failed' | 'successful' | 'postponed';
  reason?: string;
  notes?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  images?: string[];
}

export const ORDER_STATUSES = [
  { key: 'pending' as OrderStatus, label: 'في الانتظار', color: 'yellow' },
  { key: 'confirmed' as OrderStatus, label: 'مؤكد', color: 'blue' },
  { key: 'dispatched' as OrderStatus, label: 'تم الإرسال', color: 'purple' },
  { key: 'delivered' as OrderStatus, label: 'تم التسليم', color: 'green' },
  { key: 'returned' as OrderStatus, label: 'مرتجع', color: 'orange' },
  { key: 'cancelled' as OrderStatus, label: 'ملغي', color: 'red' },
  { key: 'postponed' as OrderStatus, label: 'مؤجل', color: 'gray' },
];

export const PAYMENT_METHODS = [
  { key: 'cash', label: 'نقدي' },
  { key: 'card', label: 'بطاقة' },
  { key: 'transfer', label: 'تحويل' },
  { key: 'wallet', label: 'محفظة إلكترونية' },
];

export const PRIORITY_LEVELS = [
  { key: 'low', label: 'منخفض', color: 'gray' },
  { key: 'normal', label: 'عادي', color: 'blue' },
  { key: 'high', label: 'عالي', color: 'orange' },
  { key: 'urgent', label: 'عاجل', color: 'red' },
];
