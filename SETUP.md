# دليل إعداد وتشغيل تطبيق Rihla Delivery

## متطلبات النظام

### للتطوير على الويب:
- Node.js 18 أو أحدث
- npm أو yarn أو pnpm
- محرر نصوص (VS Code مُوصى به)

### للتطوير على الأندرويد:
- Android Studio
- Android SDK
- Java Development Kit (JDK) 11 أو أحدث

### للتطوير على iOS:
- macOS
- Xcode 14 أو أحدث
- iOS SDK

## خطوات التثبيت

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd rihla-delivery-app
```

### 2. تثبيت المكتبات
```bash
npm install
```

### 3. تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

سيتم فتح التطبيق على `http://localhost:3000`

## إعداد التطبيقات المحمولة

### إعداد الأندرويد

1. **تثبيت Android Studio**
   - حمل وثبت Android Studio من الموقع الرسمي
   - تأكد من تثبيت Android SDK

2. **إضافة منصة الأندرويد**
   ```bash
   npm run cap:add android
   ```

3. **بناء ومزامنة التطبيق**
   ```bash
   npm run cap:build
   ```

4. **فتح المشروع في Android Studio**
   ```bash
   npm run android
   ```

### إعداد iOS

1. **تثبيت Xcode**
   - حمل وثبت Xcode من App Store
   - تأكد من تثبيت iOS SDK

2. **إضافة منصة iOS**
   ```bash
   npm run cap:add ios
   ```

3. **بناء ومزامنة التطبيق**
   ```bash
   npm run cap:build
   ```

4. **فتح المشروع في Xcode**
   ```bash
   npm run ios
   ```

## الحسابات التجريبية

يمكنك استخدام هذه الحسابات لتجربة التطبيق:

| الدور | البريد الإلكتروني | كلمة المرور | الصلاحيات |
|-------|------------------|-------------|-----------|
| مدير | <EMAIL> | password | جميع الصلاحيات |
| مشرف | <EMAIL> | password | صلاحيات محدودة |
| موظف | <EMAIL> | password | صلاحيات أساسية |

## المميزات المتاحة

### ✅ المميزات المكتملة:
- تسجيل الدخول ونظام الصلاحيات
- لوحة التحكم الرئيسية
- البحث السريع عن الطلبات
- صفحة الإعدادات (الملف الشخصي، الإشعارات، قاعدة البيانات)
- صفحة حذف الطلبات المجمع
- صفحة مسح الباركود
- صفحة الاستيراد والتصدير
- دعم الأجهزة المحمولة (Capacitor)
- واجهة عربية مع دعم RTL

### 🚧 قيد التطوير:
- إدارة الطلبات الكاملة
- نظام الإسناد
- إدارة الرواجع
- المحاسبة
- الأرشيف
- إدارة المستخدمين
- الإشعارات
- مشاركة الصور

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تثبيت المكتبات**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **مشاكل في Capacitor**
   ```bash
   npm run cap:sync
   ```

3. **مشاكل في البناء**
   ```bash
   npm run build
   ```

### لوجات مفيدة:
- تحقق من console المتصفح للأخطاء
- استخدم `npm run dev` لرؤية الأخطاء في الوقت الفعلي
- تحقق من لوجات Android Studio أو Xcode للمشاكل المحمولة

## التطوير

### إضافة صفحة جديدة:
1. أنشئ ملف في مجلد `app/`
2. أضف الصفحة إلى نظام التنقل في `components/dashboard-page.tsx`
3. تأكد من إضافة الصلاحيات المطلوبة في `lib/permissions.ts`

### إضافة مكون UI جديد:
1. أنشئ المكون في `components/ui/`
2. اتبع نمط shadcn/ui
3. أضف التصدير في ملف index إذا لزم الأمر

### تعديل الصلاحيات:
1. عدل `lib/permissions.ts`
2. أضف الصلاحيات الجديدة للأدوار المختلفة
3. تأكد من تحديث واجهة المستخدم وفقاً للصلاحيات

## النشر

### نشر على الويب:
```bash
npm run build
npm start
```

### بناء للأندرويد:
```bash
npm run cap:build
# ثم استخدم Android Studio لبناء APK
```

### بناء لـ iOS:
```bash
npm run cap:build
# ثم استخدم Xcode لبناء IPA
```

## الدعم

للحصول على المساعدة:
1. تحقق من هذا الدليل أولاً
2. ابحث في Issues على GitHub
3. أنشئ Issue جديد مع تفاصيل المشكلة
