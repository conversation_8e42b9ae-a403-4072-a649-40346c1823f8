{"name": "rihla-delivery-app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "cap:add": "npx cap add", "cap:copy": "npx cap copy", "cap:sync": "npx cap sync", "cap:open": "npx cap open", "cap:run": "npx cap run", "cap:build": "npm run build && npx cap sync", "android": "npm run cap:build && npx cap open android", "ios": "npm run cap:build && npx cap open ios", "firebase:emulators": "firebase emulators:start", "firebase:deploy": "npm run build && firebase deploy", "firebase:init": "firebase init"}, "dependencies": {"@capacitor/android": "^5.6.0", "@capacitor/app": "^5.0.6", "@capacitor/camera": "^5.0.7", "@capacitor/core": "^5.6.0", "@capacitor/haptics": "^5.0.6", "@capacitor/ios": "^5.6.0", "@capacitor/keyboard": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@capacitor/toast": "^5.0.6", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-table": "^8.11.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^3.0.6", "firebase": "^10.7.1", "lucide-react": "^0.303.0", "react-day-picker": "^8.10.0", "next": "14.0.4", "xlsx": "^0.18.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@capacitor/cli": "^5.6.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/xlsx": "^0.0.36", "firebase-tools": "^13.0.2", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}