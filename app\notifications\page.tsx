"use client";

import React from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ArrowRight, Home, BellOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function NotificationsPage() {
  const router = useRouter();

  // Placeholder for notifications data
  const notifications: any[] = [
    // { id: "1", title: "تحديث حالة طلب", message: "تم تحديث طلب SWT-001 إلى تم التسليم.", date: "2024-05-28", read: false },
    // { id: "2", title: "طلب جديد مسند", message: "تم إسناد طلب SWT-005 إليك.", date: "2024-05-27", read: true },
  ];

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="الإشعارات"
        description="جميع التنبيهات والتحديثات الخاصة بحسابك ونشاطاتك."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowRight className="h-4 w-4" />
              رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle>قائمة الإشعارات</CardTitle>
        </CardHeader>
        <CardContent>
          {notifications.length === 0 ? (
            <div className="text-center py-16 text-muted-foreground">
              <BellOff className="mx-auto h-16 w-16 mb-4 text-gray-400" />
              <p className="text-xl font-semibold">لا توجد إشعارات جديدة حالياً.</p>
              <p className="mt-2">سيتم عرض التنبيهات والتحديثات الهامة هنا عند توفرها.</p>
            </div>
          ) : (
            <ul className="space-y-4">
              {notifications.map((notification) => (
                <li 
                  key={notification.id} 
                  className={`p-4 rounded-md border ${notification.read ? 'bg-muted/50' : 'bg-card hover:bg-muted/20'}`}
                >
                  <h3 className={`font-semibold ${!notification.read ? 'text-primary' : ''}`}>{notification.title}</h3>
                  <p className="text-sm text-muted-foreground">{notification.message}</p>
                  <p className="text-xs text-muted-foreground mt-1">{notification.date}</p>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
