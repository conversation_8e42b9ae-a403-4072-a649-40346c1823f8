"use client";

import { Capacitor } from '@capacitor/core';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Toast } from '@capacitor/toast';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { StatusBar, Style } from '@capacitor/status-bar';

export const isNative = () => Capacitor.isNativePlatform();
export const isWeb = () => !Capacitor.isNativePlatform();

// Camera utilities
export const takePicture = async () => {
  try {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.DataUrl,
      source: CameraSource.Camera,
    });
    return image.dataUrl;
  } catch (error) {
    console.error('Error taking picture:', error);
    throw error;
  }
};

export const selectImage = async () => {
  try {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.DataUrl,
      source: CameraSource.Photos,
    });
    return image.dataUrl;
  } catch (error) {
    console.error('Error selecting image:', error);
    throw error;
  }
};

// Toast utilities
export const showToast = async (message: string, duration: 'short' | 'long' = 'short') => {
  if (isNative()) {
    try {
      await Toast.show({
        text: message,
        duration: duration,
        position: 'bottom',
      });
    } catch (error) {
      console.error('Error showing toast:', error);
    }
  }
};

// Haptics utilities
export const hapticImpact = async (style: ImpactStyle = ImpactStyle.Medium) => {
  if (isNative()) {
    try {
      await Haptics.impact({ style });
    } catch (error) {
      console.error('Error with haptic feedback:', error);
    }
  }
};

// Status bar utilities
export const setStatusBarStyle = async (style: Style = Style.Default) => {
  if (isNative()) {
    try {
      await StatusBar.setStyle({ style });
    } catch (error) {
      console.error('Error setting status bar style:', error);
    }
  }
};

export const hideStatusBar = async () => {
  if (isNative()) {
    try {
      await StatusBar.hide();
    } catch (error) {
      console.error('Error hiding status bar:', error);
    }
  }
};

export const showStatusBar = async () => {
  if (isNative()) {
    try {
      await StatusBar.show();
    } catch (error) {
      console.error('Error showing status bar:', error);
    }
  }
};

// Platform detection utilities
export const getPlatform = () => Capacitor.getPlatform();
export const isAndroid = () => Capacitor.getPlatform() === 'android';
export const isIOS = () => Capacitor.getPlatform() === 'ios';
