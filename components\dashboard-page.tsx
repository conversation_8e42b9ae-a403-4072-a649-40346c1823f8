"use client";

import { <PERSON>Header } from "@/components/page-header";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import Link from "next/link";
import {
  Archive,
  Calculator,
  Combine,
  Package,
  Send,
  Settings,
  Users,
  ArchiveRestore,
  Search,
  Camera,
  type LucideIcon,
  Bell,
  Image,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { getOrder } from "@/lib/order-data"; 
import React, { useMemo, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import type { AppSectionId } from "@/lib/permissions";


interface NavItem {
  href: string;
  label: string;
  icon: LucideIcon;
  description: string;
  iconClassName?: string;
  id: AppSectionId; 
}

const allNavItems: NavItem[] = [
  { id: "orders", href: "/orders", label: "إدارة الطلبات", icon: Package, description: "عرض وإضافة وتعديل الطلبات.", iconClassName: "text-primary" },
  { id: "dispatch", href: "/dispatch", label: "إسناد الطلبات", icon: Send, description: "إسناد الطلبات للمندوبين أو الفروع.", iconClassName: "text-purple-500" },
  { id: "returns", href: "/returns", label: "إدارة الرواجع", icon: ArchiveRestore, description: "استلام وتسليم الطلبات الراجعة.", iconClassName: "text-orange-500" },
  { id: "accounting", href: "/accounting", label: "المحاسبة", icon: Calculator, description: "إدارة محاسبة المندوبين والعملاء.", iconClassName: "text-green-500" },
  { id: "archive", href: "/archive", label: "الأرشيف", icon: Archive, description: "عرض الطلبات المؤرشفة.", iconClassName: "text-gray-500" },
  { id: "users", href: "/users", label: "إدارة الموظفين", icon: Users, description: "إدارة حسابات وصلاحيات المستخدمين.", iconClassName: "text-teal-500" },
  { id: "importExport", href: "/import-export", label: "استيراد/تصدير/حذف", icon: Combine, description: "عمليات مجمعة على الطلبات.", iconClassName: "text-indigo-500" },
  { id: "notifications", href: "/notifications", label: "الإشعارات", icon: Bell, description: "عرض الإشعارات والتنبيهات.", iconClassName: "text-yellow-500" },
  { id: "imageShare", href: "/image-share", label: "مشاركة الصور", icon: Image, description: "اختر صورة وشاركها عبر التطبيقات.", iconClassName: "text-cyan-500" },
  { id: "settings", href: "/settings", label: "الإعدادات", icon: Settings, description: "إدارة إعدادات حسابك والتطبيق.", iconClassName: "text-pink-500" },
];


export default function DashboardPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { currentUser } = useAuth();

  const [searchInputValue, setSearchInputValue] = useState("");

  const handleQuickSearch = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const searchTermTrimmed = searchInputValue.trim();
    if(searchTermTrimmed) {
        const foundOrder = await getOrder(searchTermTrimmed);
        if (foundOrder) {
          router.push(`/orders/${foundOrder.id}`);
        } else {
          router.push(`/orders?search=${searchTermTrimmed}`);
        }
    } else {
        toast({variant: "destructive", title: "خطأ", description: "يرجى إدخال رقم وصل أو رقم موبايل للبحث."});
    }
  }

  const navItemsToDisplay = useMemo(() => {
    if (!currentUser?.permissions) return [];
    return allNavItems.filter(item => {
      return currentUser.permissions[item.id]?.view === true;
    });
  }, [currentUser]);
  
  return (
    <>
      <PageHeader 
        title="لوحة التحكم الرئيسية" 
        description={`مرحباً بك، ${currentUser?.name || ''}. اختر القسم الذي تريد إدارته.`}
      />
      
      <Card className="mb-6 md:mb-8 shadow-lg rounded-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-6 w-6 text-primary" />
            بحث سريع عن طلب
          </CardTitle>
          <CardDescription>
            أدخل رقم الوصل أو رقم موبايل المستلم للبحث السريع أو استخدم الكاميرا للباركود.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleQuickSearch} className="flex items-center gap-2">
            <Input 
              name="searchQuery"
              type="search" 
              placeholder="رقم الوصل..." 
              className="flex-grow"
              value={searchInputValue}
              onChange={(e) => setSearchInputValue(e.target.value)}
            />
            <Button variant="outline" size="icon" onClick={() => router.push('/scanner')} aria-label="بحث بالباركود" type="button">
              <Camera className="h-5 w-5" />
            </Button>
            <Button type="submit">بحث</Button>
          </form>
        </CardContent>
      </Card>
      
      <div className="grid gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {navItemsToDisplay.map((item) => (
          <Link href={item.href} key={item.href} className="block group">
            <Card className="relative h-full transform-gpu overflow-hidden rounded-xl border-2 border-primary bg-card text-card-foreground shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-primary/20">
               <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-accent/10 opacity-0 transition-opacity duration-500 group-hover:opacity-100" aria-hidden="true" />
               <div className="relative flex h-full flex-col items-center justify-center p-6 text-center">
                  <CardHeader className="p-0 mb-3 items-center">
                    <div className="mb-4 rounded-full bg-primary/10 p-4 transition-colors duration-300 group-hover:bg-primary/20">
                       <item.icon className={cn("h-10 w-10 mx-auto transition-transform duration-300 group-hover:scale-110", item.iconClassName || 'text-primary')} />
                    </div>
                    <CardTitle className="text-xl font-bold">{item.label}</CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <p className="text-sm text-muted-foreground">{item.description}</p>
                  </CardContent>
               </div>
            </Card>
          </Link>
        ))}
      </div>
    </>
  );
}
