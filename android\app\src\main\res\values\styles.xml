<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base Application Theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent</item>
        
        <!-- Background colors -->
        <item name="android:windowBackground">@color/background</item>
        <item name="android:colorBackground">@color/background</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/primary_dark</item>
        
        <!-- Window properties -->
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <item name="android:windowAllowReturnTransitionOverlap">true</item>
        <item name="android:windowSharedElementEnterTransition">@android:transition/move</item>
        <item name="android:windowSharedElementExitTransition">@android:transition/move</item>
        
        <!-- RTL Support -->
        <item name="android:supportsRtl">true</item>
        <item name="android:layoutDirection">rtl</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="AppTheme.NoActionBarLaunch" parent="AppTheme">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_screen</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@color/splash_background</item>
        <item name="android:navigationBarColor">@color/splash_background</item>
    </style>

    <!-- No Action Bar Theme -->
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!-- Full Screen Theme -->
    <style name="AppTheme.FullScreen" parent="AppTheme.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Dark Theme -->
    <style name="AppTheme.Dark" parent="Theme.AppCompat.DayNight">
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryDark">@color/primary</item>
        <item name="colorAccent">@color/accent_light</item>
        
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:colorBackground">@color/background_dark</item>
        
        <item name="android:textColorPrimary">@color/text_on_dark</item>
        <item name="android:textColorSecondary">@color/secondary_light</item>
        
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:navigationBarColor">@color/background_dark</item>
    </style>

    <!-- Button Styles -->
    <style name="ButtonPrimary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_primary_background</item>
        <item name="android:textColor">@color/button_text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingLeft">24dp</item>
        <item name="android:paddingRight">24dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:elevation">2dp</item>
    </style>

    <style name="ButtonSecondary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_secondary_background</item>
        <item name="android:textColor">@color/button_text_secondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingLeft">24dp</item>
        <item name="android:paddingRight">24dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:elevation">1dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TextHeading1">
        <item name="android:textSize">32sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="TextHeading2">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <style name="TextHeading3">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextBody1">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="TextBody2">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <style name="TextCaption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <!-- Card Styles -->
    <style name="CardStyle" parent="CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- Input Styles -->
    <style name="InputStyle" parent="Widget.AppCompat.EditText">
        <item name="android:background">@drawable/input_background</item>
        <item name="android:textColor">@color/input_text</item>
        <item name="android:textColorHint">@color/input_hint</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- Progress Bar Styles -->
    <style name="ProgressBarStyle" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressDrawable">@drawable/progress_bar_background</item>
        <item name="android:minHeight">4dp</item>
        <item name="android:maxHeight">4dp</item>
    </style>

    <!-- Toolbar Styles -->
    <style name="ToolbarStyle" parent="Widget.AppCompat.Toolbar">
        <item name="android:background">@color/primary</item>
        <item name="android:theme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="popupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="android:elevation">4dp</item>
    </style>

    <!-- Tab Layout Styles -->
    <style name="TabLayoutStyle" parent="Widget.Design.TabLayout">
        <item name="tabIndicatorColor">@color/accent</item>
        <item name="tabSelectedTextColor">@color/primary</item>
        <item name="tabTextColor">@color/text_secondary</item>
        <item name="tabBackground">@color/background</item>
    </style>

    <!-- Bottom Navigation Styles -->
    <style name="BottomNavigationStyle" parent="Widget.Design.BottomNavigationView">
        <item name="android:background">@color/navigation_background</item>
        <item name="itemIconTint">@color/navigation_item</item>
        <item name="itemTextColor">@color/navigation_item</item>
        <item name="android:elevation">8dp</item>
    </style>

    <!-- Floating Action Button Styles -->
    <style name="FABStyle" parent="Widget.Design.FloatingActionButton">
        <item name="backgroundTint">@color/accent</item>
        <item name="tint">@color/white</item>
        <item name="android:layout_margin">16dp</item>
        <item name="elevation">6dp</item>
        <item name="pressedTranslationZ">12dp</item>
    </style>

    <!-- Dialog Styles -->
    <style name="DialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
    </style>

    <!-- Notification Styles -->
    <style name="NotificationStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@color/notification_background</item>
        <item name="android:padding">16dp</item>
        <item name="android:elevation">4dp</item>
    </style>

</resources>
