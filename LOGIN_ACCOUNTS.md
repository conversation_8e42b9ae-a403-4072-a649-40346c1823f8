# 🔑 دليل حسابات تسجيل الدخول - Rihla Delivery

## ✅ **التطبيق يعمل الآن بنجاح!**

### 🌐 **الرابط**: http://localhost:3000

---

## 📋 جميع أسماء المستخدمين وكلمات المرور

### **🏢 الإدارة العليا:**

#### **1. المدير العام**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `password`
- **الصلاحيات**: جميع الصلاحيات، إدارة كاملة للنظام

#### **2. المشرف**
- **اسم المستخدم**: `manager`
- **كلمة المرور**: `password`
- **الصلاحيات**: إدارة الطلبات والموظفين، التقارير

#### **3. الموظف**
- **اسم المستخدم**: `employee`
- **كلمة المرور**: `password`
- **الصلاحيات**: إدارة الطلبات الأساسية

---

### **🏬 إدارة المراكز والفروع:**

#### **4. مدير المركز**
- **اسم المستخدم**: `center1`
- **كلمة المرور**: `password`
- **الصلاحيات**: إدارة المركز والفروع التابعة له

#### **5. مدير الفرع**
- **اسم المستخدم**: `branch1`
- **كلمة المرور**: `password`
- **الصلاحيات**: إدارة الفرع والمندوبين التابعين له

---

### **🚚 المندوبين:**

#### **6. المندوب الأول - أحمد علي**
- **اسم المستخدم**: `rep1`
- **كلمة المرور**: `password`
- **المنطقة**: بغداد - الكرخ

#### **7. المندوب الثاني - محمد حسن**
- **اسم المستخدم**: `rep2`
- **كلمة المرور**: `password`
- **المنطقة**: بغداد - الرصافة

#### **8. المندوب الثالث - علي محمود**
- **اسم المستخدم**: `rep3`
- **كلمة المرور**: `password`
- **المنطقة**: البصرة

---

### **💼 الأدوار الأخرى:**

#### **9. المحاسب**
- **اسم المستخدم**: `accountant1`
- **كلمة المرور**: `password`
- **الصلاحيات**: إدارة المحاسبة والتسويات المالية

#### **10. المرسل**
- **اسم المستخدم**: `dispatcher1`
- **كلمة المرور**: `password`
- **الصلاحيات**: إسناد وتوزيع الطلبات

#### **11. العميل الأول**
- **اسم المستخدم**: `client1`
- **كلمة المرور**: `password`
- **الصلاحيات**: عرض طلباته الخاصة

#### **12. المتابع**
- **اسم المستخدم**: `follower1`
- **كلمة المرور**: `password`
- **الصلاحيات**: متابعة الطلبات والتقارير

#### **13. الحساب الافتراضي** ⭐
- **اسم المستخدم**: `default`
- **كلمة المرور**: `123456`
- **الصلاحيات**: حساب افتراضي للاختبار السريع

---

## 🚀 كيفية تسجيل الدخول

### **الطريقة الأولى - الدخول السريع:**
1. افتح التطبيق على http://localhost:3000
2. انقر على أي من أزرار الحسابات التجريبية
3. انقر على "تسجيل الدخول"

### **الطريقة الثانية - الدخول اليدوي:**
1. أدخل اسم المستخدم (مثل: `admin`)
2. أدخل كلمة المرور: `password`
3. انقر على "تسجيل الدخول"

---

## 🎯 اختبار الأدوار المختلفة

### **لاختبار صلاحيات المدير:**
- سجل دخول بـ `admin` / `password`
- ستحصل على جميع الصلاحيات

### **لاختبار صلاحيات المندوب:**
- سجل دخول بـ `rep1` / `password`
- ستحصل على واجهة المندوب

### **لاختبار صلاحيات المحاسب:**
- سجل دخول بـ `accountant1` / `password`
- ستحصل على صلاحيات المحاسبة

---

## 📊 الميزات المتاحة لكل دور

### **المدير العام (admin):**
- ✅ جميع الميزات
- ✅ إدارة المستخدمين
- ✅ محاسبة المندوبين
- ✅ التقارير الشاملة
- ✅ إعدادات النظام

### **المشرف (manager):**
- ✅ إدارة الطلبات
- ✅ إدارة الموظفين
- ✅ التقارير
- ✅ المحاسبة

### **المندوب (rep1, rep2, rep3):**
- ✅ عرض الطلبات المسندة إليه
- ✅ تحديث حالة الطلبات
- ✅ عرض المحاسبة الخاصة به

### **المحاسب (accountant1):**
- ✅ محاسبة المندوبين
- ✅ التقارير المالية
- ✅ أرشيف المحاسبات

### **مدير المركز (center1):**
- ✅ إدارة الفروع التابعة
- ✅ إسناد الطلبات للفروع
- ✅ التقارير الخاصة بالمركز

### **مدير الفرع (branch1):**
- ✅ إدارة المندوبين التابعين
- ✅ إسناد الطلبات للمندوبين
- ✅ التقارير الخاصة بالفرع

---

## 🔐 ملاحظات أمنية

### **للاختبار فقط:**
- جميع كلمات المرور هي `password`
- هذه حسابات تجريبية للاختبار فقط
- في الإنتاج، استخدم كلمات مرور قوية

### **في الإنتاج:**
- غيّر جميع كلمات المرور
- فعّل التشفير
- أضف التحقق بخطوتين
- استخدم قاعدة بيانات حقيقية

---

## 🆘 المساعدة

### **إذا لم تعمل بيانات الدخول:**
1. تأكد من تشغيل التطبيق: `npm run dev`
2. تأكد من الرابط: http://localhost:3000
3. جرب إعادة تحميل الصفحة
4. تحقق من وحدة التحكم للأخطاء

### **للحصول على مساعدة إضافية:**
- راجع `README.md`
- راجع `QUICK_START.md`
- راجع `ANDROID_SETUP.md`

---

**نصيحة:** احفظ هذا الملف كمرجع سريع لجميع بيانات الدخول! 📚
