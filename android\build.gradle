// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath 'com.google.gms:google-services:4.3.15'
        
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

apply from: "variables.gradle"

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

// Global configuration
ext {
    // SDK and tool versions
    compileSdkVersion = 34
    targetSdkVersion = 34
    minSdkVersion = 22
    
    // App versioning
    versionCode = 1
    versionName = "1.0.0"
    
    // Dependency versions
    androidxAppCompatVersion = '1.6.1'
    androidxCoordinatorLayoutVersion = '1.2.0'
    androidxCoreVersion = '1.10.1'
    androidxFragmentVersion = '1.6.0'
    coreSplashScreenVersion = '1.0.1'
    androidxActivityVersion = '1.7.2'
    androidxJunitVersion = '1.1.5'
    androidxEspressoCoreVersion = '3.5.1'
    junitVersion = '4.13.2'
    
    // Capacitor versions
    capacitorVersion = '5.0.0'
    
    // Build tools
    gradleVersion = '8.0.2'
    kotlinVersion = '1.8.22'
    
    // Other libraries
    materialVersion = '1.9.0'
    constraintLayoutVersion = '2.1.4'
    recyclerViewVersion = '1.3.0'
    cardViewVersion = '1.0.0'
    
    // Network libraries
    okhttpVersion = '4.11.0'
    retrofitVersion = '2.9.0'
    gsonVersion = '2.10.1'
    
    // Image loading
    glideVersion = '4.15.1'
    
    // Permissions
    easyPermissionsVersion = '3.0.0'
    
    // Firebase (if used)
    firebaseBomVersion = '32.2.2'
    
    // Testing
    mockitoVersion = '4.11.0'
    robolectricVersion = '4.10.3'
    espressoVersion = '3.5.1'
    
    // Code quality
    spotbugsVersion = '4.7.3'
    pmdVersion = '6.55.0'
    checkstyleVersion = '10.12.1'
}

// Configure all projects
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            android {
                compileSdkVersion rootProject.ext.compileSdkVersion
                
                defaultConfig {
                    minSdkVersion rootProject.ext.minSdkVersion
                    targetSdkVersion rootProject.ext.targetSdkVersion
                }
                
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_1_8
                    targetCompatibility JavaVersion.VERSION_1_8
                }
                
                // Common lint options
                lintOptions {
                    abortOnError false
                    checkReleaseBuilds false
                    disable 'MissingTranslation', 'ExtraTranslation'
                }
                
                // Common packaging options
                packagingOptions {
                    exclude 'META-INF/DEPENDENCIES'
                    exclude 'META-INF/LICENSE'
                    exclude 'META-INF/LICENSE.txt'
                    exclude 'META-INF/NOTICE'
                    exclude 'META-INF/NOTICE.txt'
                    exclude 'META-INF/ASL2.0'
                    exclude 'META-INF/*.kotlin_module'
                }
            }
        }
    }
}

// Custom tasks for project management
task generateProjectInfo {
    doLast {
        def projectInfoFile = file('project-info.json')
        projectInfoFile.text = """
{
    "projectName": "Rihla Delivery",
    "packageName": "com.rihla.delivery",
    "versionName": "${versionName}",
    "versionCode": ${versionCode},
    "compileSdkVersion": ${compileSdkVersion},
    "targetSdkVersion": ${targetSdkVersion},
    "minSdkVersion": ${minSdkVersion},
    "buildTime": "${new Date().format('yyyy-MM-dd HH:mm:ss')}",
    "gradleVersion": "${gradleVersion}",
    "capacitorVersion": "${capacitorVersion}"
}
"""
        println "Project info generated: ${projectInfoFile.absolutePath}"
    }
}

// Task to check for dependency updates
task checkDependencyUpdates {
    doLast {
        println "Checking for dependency updates..."
        println "Current Gradle version: ${gradleVersion}"
        println "Current Capacitor version: ${capacitorVersion}"
        println "Current Android Gradle Plugin version: 8.0.2"
        println "Run './gradlew dependencyUpdates' for detailed dependency update report"
    }
}

// Task to clean all build artifacts
task cleanAll {
    dependsOn clean
    doLast {
        delete fileTree(dir: '.', include: '**/*.iml')
        delete fileTree(dir: '.', include: '**/build')
        delete file('.gradle')
        delete file('local.properties')
        println "All build artifacts cleaned"
    }
}

// Task to prepare for release build
task prepareRelease {
    dependsOn generateProjectInfo
    doLast {
        println "Preparing release build..."
        println "Version: ${versionName} (${versionCode})"
        println "Target SDK: ${targetSdkVersion}"
        println "Min SDK: ${minSdkVersion}"
        println "Make sure to:"
        println "1. Update version numbers if needed"
        println "2. Configure signing keys"
        println "3. Test on multiple devices"
        println "4. Update release notes"
    }
}

// Wrapper task configuration
wrapper {
    gradleVersion = '8.0.2'
    distributionType = Wrapper.DistributionType.ALL
}
