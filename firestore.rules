rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserData().role == 'admin';
    }
    
    function isManager() {
      return isAuthenticated() && (getUserData().role == 'admin' || getUserData().role == 'manager');
    }
    
    function isActiveUser() {
      return isAuthenticated() && getUserData().isActive == true;
    }
    
    function belongsToSameCompany(companyId) {
      return isAuthenticated() && getUserData().companyId == companyId;
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        request.auth.uid == userId || 
        isManager()
      );
      allow create: if isAdmin();
      allow update: if isAuthenticated() && (
        request.auth.uid == userId || 
        isAdmin()
      );
      allow delete: if isAdmin();
    }
    
    // Companies collection
    match /companies/{companyId} {
      allow read: if isActiveUser() && (
        isAdmin() || 
        belongsToSameCompany(companyId)
      );
      allow create: if isAdmin();
      allow update: if isActiveUser() && (
        isAdmin() || 
        (isManager() && belongsToSameCompany(companyId))
      );
      allow delete: if isAdmin();
    }
    
    // Company settings
    match /companySettings/{settingId} {
      allow read, write: if isActiveUser() && (
        isAdmin() || 
        (isManager() && belongsToSameCompany(resource.data.companyId))
      );
    }
    
    // Company stats
    match /companyStats/{statId} {
      allow read: if isActiveUser() && (
        isAdmin() || 
        belongsToSameCompany(resource.data.companyId)
      );
      allow write: if isAdmin();
    }
    
    // Orders collection
    match /orders/{orderId} {
      allow read: if isActiveUser() && (
        isAdmin() || 
        belongsToSameCompany(resource.data.companyId) ||
        getUserData().id == resource.data.agentId
      );
      allow create: if isActiveUser() && (
        isAdmin() || 
        isManager() ||
        getUserData().role == 'employee'
      );
      allow update: if isActiveUser() && (
        isAdmin() || 
        isManager() ||
        getUserData().role == 'employee' ||
        getUserData().id == resource.data.agentId
      );
      allow delete: if isAdmin() || (
        isManager() && belongsToSameCompany(resource.data.companyId)
      );
    }
    
    // Order history
    match /orderHistory/{historyId} {
      allow read: if isActiveUser() && (
        isAdmin() || 
        isManager()
      );
      allow create: if isActiveUser();
      allow update, delete: if isAdmin();
    }
    
    // Delivery attempts
    match /deliveryAttempts/{attemptId} {
      allow read: if isActiveUser() && (
        isAdmin() || 
        isManager() ||
        getUserData().id == resource.data.agentId
      );
      allow create: if isActiveUser() && (
        getUserData().role == 'مندوب' ||
        isManager() ||
        isAdmin()
      );
      allow update, delete: if isAdmin() || isManager();
    }
    
    // Notifications
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && (
        request.auth.uid == resource.data.userId ||
        isAdmin()
      );
      allow create: if isActiveUser();
      allow update: if isAuthenticated() && (
        request.auth.uid == resource.data.userId ||
        isAdmin()
      );
      allow delete: if isAdmin();
    }
    
    // Notification templates
    match /notificationTemplates/{templateId} {
      allow read: if isActiveUser();
      allow write: if isAdmin() || isManager();
    }
    
    // Notification settings
    match /notificationSettings/{settingId} {
      allow read, write: if isAuthenticated() && (
        request.auth.uid == resource.data.userId ||
        isAdmin()
      );
    }
    
    // Push tokens
    match /pushTokens/{tokenId} {
      allow read, write: if isAuthenticated() && (
        request.auth.uid == resource.data.userId ||
        isAdmin()
      );
    }
    
    // Allow read access to public collections for demo purposes
    // In production, you might want to restrict this further
    match /{document=**} {
      allow read: if true; // For demo purposes only
    }
  }
}
