"use client";

import { PageHeader } from "@/components/page-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Camera, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { takePicture, isNative } from "@/lib/capacitor";
import { useToast } from "@/hooks/use-toast";

export default function ScannerPage() {
  const router = useRouter();
  const { toast } = useToast();

  const handleScan = async () => {
    try {
      if (isNative()) {
        const imageData = await takePicture();
        // Here you would implement barcode scanning logic
        toast({
          title: "تم التقاط الصورة",
          description: "سيتم إضافة مسح الباركود قريباً"
        });
      } else {
        toast({
          title: "غير متاح",
          description: "مسح الباركود متاح فقط في التطبيق المحمول"
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "فشل في تشغيل الكاميرا"
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader 
        title="مسح الباركود" 
        description="استخدم الكاميرا لمسح باركود الطلب"
      >
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowRight className="h-4 w-4 ml-2" />
          رجوع
        </Button>
      </PageHeader>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-6 w-6" />
            مسح باركود الطلب
          </CardTitle>
          <CardDescription>
            اضغط على الزر أدناه لتشغيل الكاميرا ومسح الباركود
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div className="w-48 h-48 mx-auto border-2 border-dashed border-muted-foreground rounded-lg flex items-center justify-center">
            <Camera className="h-16 w-16 text-muted-foreground" />
          </div>
          
          <Button onClick={handleScan} size="lg" className="w-full max-w-sm">
            <Camera className="h-5 w-5 ml-2" />
            تشغيل الكاميرا
          </Button>
          
          <p className="text-sm text-muted-foreground">
            {isNative() 
              ? "وجه الكاميرا نحو الباركود للمسح التلقائي"
              : "مسح الباركود متاح فقط في التطبيق المحمول"
            }
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
