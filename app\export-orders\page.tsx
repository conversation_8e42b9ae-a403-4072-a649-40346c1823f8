"use client";

import React, { useState, useMemo, useEffect } from 'react';
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import type { DateRange } from "react-day-picker";
import { useToast } from "@/hooks/use-toast";
import { getOrders, ORDER_STATUSES_DETAILS, getOrderStatusDetail } from "@/lib/order-data";
import { getUsers } from "@/lib/user-data";
import type { Order, OrderStatus, User } from "@/types";
import { Download, ArrowRight, Users as UsersIcon, CalendarDays, FileSpreadsheet, <PERSON>, ListFilter, Loader2 } from "lucide-react";
import { useRouter } from 'next/navigation';
import * as XLSX from 'xlsx';
import Link from "next/link";
import { format } from 'date-fns';

interface ExportedOrder {
  "رقم الوصل": string;
  "شركة العميل (المرسل)": string;
  "هاتف شركة العميل": string | undefined;
  "اسم الزبون (المستلم)": string | undefined;
  "هاتف الزبون (المستلم)": string;
  "عنوان الزبون (المستلم)": string | undefined;
  "المبلغ الأصلي": number;
  "المبلغ المحدث": number | undefined;
  "الحالة": string;
  "المندوب المسند إليه": string;
  "تاريخ الإنشاء": string;
  "تاريخ آخر تحديث": string;
  "ملاحظات": string | undefined;
  "سبب الإرجاع": string | undefined;
  "سبب التأجيل": string | undefined;
  "مواد مرتجعة جزئياً": number | undefined;
  "معرف العميل المستورد": string | undefined;
}

const allOrderStatusesForFilter: { value: OrderStatus | "all"; label: string }[] = [
  { value: "all", label: "جميع الحالات" },
  ...ORDER_STATUSES_DETAILS.map(statusDetail => ({ value: statusDetail.key, label: statusDetail.label })),
];

export default function ExportOrdersPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [selectedRepId, setSelectedRepId] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | "all">("all");
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const [orders, users] = await Promise.all([getOrders(), getUsers()]);
        setAllOrders(orders);
        setAllUsers(users);
      } catch (e) {
        toast({variant: "destructive", title: "خطأ", description: "فشل تحميل البيانات."});
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [toast]);
  
  const representatives = useMemo(() =>
    allUsers.filter(emp => emp.role === 'مندوب' && (emp.isActive === undefined || emp.isActive)),
    [allUsers]
  );

  const handleExport = (exportAll: boolean) => {
    setIsLoading(true);

    let ordersToExport = [...allOrders];

    if (dateRange?.from && dateRange?.to) {
      const fromDate = new Date(dateRange.from);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(dateRange.to);
      toDate.setHours(23, 59, 59, 999);

      ordersToExport = ordersToExport.filter(order => {
        const orderCreationDate = new Date(order.createdAt);
        return orderCreationDate >= fromDate && orderCreationDate <= toDate;
      });
    }

    if (!exportAll && selectedRepId !== "all") {
      ordersToExport = ordersToExport.filter(order => order.agentId === selectedRepId);
    }

    if (selectedStatus !== "all") {
      ordersToExport = ordersToExport.filter(order => order.status === selectedStatus);
    }

    if (ordersToExport.length === 0) {
      toast({
        title: "لا توجد طلبات للتصدير",
        description: "لم يتم العثور على طلبات تطابق المعايير المحددة.",
        variant: "default",
      });
      setIsLoading(false);
      return;
    }

    const dataForSheet: ExportedOrder[] = ordersToExport.map(order => {
      const statusDetail = getOrderStatusDetail(order.status);
      const assignedRepDetails = order.agentId ? allUsers.find(e => e.id === order.agentId) : null;
      const repName = assignedRepDetails ? (assignedRepDetails.name || assignedRepDetails.username) : "غير مسند";

      return {
        "رقم الوصل": order.orderNumber,
        "شركة العميل (المرسل)": order.companyName,
        "هاتف شركة العميل": order.clientPhoneNumber,
        "اسم الزبون (المستلم)": order.customerName,
        "هاتف الزبون (المستلم)": order.mobileNumber,
        "عنوان الزبون (المستلم)": order.address,
        "المبلغ الأصلي": order.amount,
        "المبلغ المحدث": order.updatedAmount,
        "الحالة": statusDetail?.label || order.status,
        "المندوب المسند إليه": repName,
        "تاريخ الإنشاء": new Date(order.createdAt).toLocaleString('ar-EG-u-nu-latn', { dateStyle: 'medium', timeStyle: 'short' }),
        "تاريخ آخر تحديث": new Date(order.updatedAt).toLocaleString('ar-EG-u-nu-latn', { dateStyle: 'medium', timeStyle: 'short' }),
        "ملاحظات": order.notes,
        "سبب الإرجاع": order.rejectedReason,
        "سبب التأجيل": order.postponeReason,
        "مواد مرتجعة جزئياً": order.partiallyReturnedItems,
        "معرف العميل المستورد": order.importedClientIdentifier,
      };
    });

    const worksheet = XLSX.utils.json_to_sheet(dataForSheet);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "الطلبات المصدرة");

    const now = new Date();
    const dateSuffix = format(now, "yyyyMMdd");
    const timeSuffix = format(now, "HHmm");
    
    let baseFilename = "Orders";
    if (!exportAll && selectedRepId !== "all") {
      const repDetails = representatives.find(r => r.id === selectedRepId);
      const repIdentifier = repDetails ? (repDetails.name || repDetails.username).replace(/\s+/g, '_') : "SelectedRep";
      baseFilename = `Rep_${repIdentifier}_Orders`;
    } else if (exportAll) {
      baseFilename = `AllOrders`;
    }

    let filename = `${baseFilename}_${dateSuffix}_${timeSuffix}.xlsx`;
    
    if (selectedStatus !== "all") {
      const statusPart = `_Status-${selectedStatus.replace(/\s+/g, '_')}`;
      filename = filename.replace('.xlsx', `${statusPart}.xlsx`);
    }

    XLSX.writeFile(workbook, filename);

    toast({
      title: "تم التصدير بنجاح",
      description: `تم تصدير ${ordersToExport.length} طلبات إلى ملف ${filename}.`,
    });
    setIsLoading(false);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="تصدير الطلبات إلى Excel"
        description="اختر المعايير لتصدير قائمة الطلبات إلى ملف Excel."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.push("/import-export")}>
              <ArrowRight className="h-4 w-4" /> رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" /> الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <Card className="max-w-2xl mx-auto shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><FileSpreadsheet className="h-6 w-6 text-primary"/> خيارات التصدير</CardTitle>
          <CardDescription>حدد النطاق الزمني، المندوب، وحالة الطلب (إذا لزم الأمر) ثم قم بالتصدير.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {isLoading && <div className="text-center py-4"><Loader2 className="h-6 w-6 animate-spin text-primary" /></div>}
          <div className={isLoading ? 'opacity-50 pointer-events-none' : ''}>
            <div>
              <Label htmlFor="date-range-picker-export" className="mb-1 block flex items-center">
                <CalendarDays className="ml-2 h-4 w-4 text-primary"/>
                تحديد النطاق الزمني للطلبات (حسب تاريخ الإنشاء)
              </Label>
              <DatePickerWithRange onDateChange={setDateRange} className="w-full" />
              <p className="text-xs text-muted-foreground mt-1">إذا لم يتم تحديد نطاق، سيتم تصدير الطلبات من جميع التواريخ.</p>
            </div>

            <div>
              <Label htmlFor="status-select-export" className="mb-1 block flex items-center">
                  <ListFilter className="ml-2 h-4 w-4 text-primary"/>
                  تصدير طلبات بحالة محددة (اختياري)
              </Label>
              <Select dir="rtl" value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as OrderStatus | "all")}>
                <SelectTrigger id="status-select-export">
                  <SelectValue placeholder="اختر حالة الطلب" />
                </SelectTrigger>
                <SelectContent>
                  {allOrderStatusesForFilter.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">إذا تم اختيار "جميع الحالات"، سيتم تجاهل هذا الفلتر.</p>
            </div>

            <div>
              <Label htmlFor="rep-select-export" className="mb-1 block flex items-center">
                  <UsersIcon className="ml-2 h-4 w-4 text-primary"/>
                  تصدير طلبات مندوب محدد (اختياري)
              </Label>
              <Select dir="rtl" value={selectedRepId} onValueChange={setSelectedRepId}>
                <SelectTrigger id="rep-select-export">
                  <SelectValue placeholder="اختر مندوبًا أو اترك للكل" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">تصدير لجميع المناديب (أو غير المسندة)</SelectItem>
                  {representatives.map(rep => (
                    <SelectItem key={rep.id} value={rep.id}>
                      {rep.name || rep.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">إذا تم اختيار "تصدير لجميع المناديب"، سيتم تجاهل هذا الفلتر عند الضغط على "تصدير جميع الطلبات".</p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button
                onClick={() => handleExport(true)}
                disabled={isLoading}
                className="flex-1"
                size="lg"
              >
                <Download className="ml-2 h-5 w-5 rtl:mr-2 rtl:ml-0" />
                {isLoading ? "جاري التصدير..." : "تصدير جميع الطلبات (حسب التاريخ/الحالة)"}
              </Button>
              <Button
                onClick={() => handleExport(false)}
                disabled={isLoading || selectedRepId === "all"}
                className="flex-1"
                variant="secondary"
                size="lg"
              >
                <Download className="ml-2 h-5 w-5 rtl:mr-2 rtl:ml-0" />
                {isLoading ? "جاري التصدير..." : "تصدير طلبات المندوب المحدد"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
