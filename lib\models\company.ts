import { Timestamp } from 'firebase/firestore';

export interface Company {
  id: string;
  name: string;
  businessName?: string;
  
  // Contact Information
  email: string;
  phoneNumber: string;
  alternativePhone?: string;
  website?: string;
  
  // Address
  address: string;
  city: string;
  country: string;
  postalCode?: string;
  
  // Business Details
  businessType: string;
  taxNumber?: string;
  commercialRegister?: string;
  
  // Settings
  isActive: boolean;
  defaultDeliveryFee: number;
  defaultCurrency: string;
  
  // Branding
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Timestamps
  createdAt: Timestamp;
  updatedAt: Timestamp;
  
  // Subscription/Plan
  subscriptionPlan: 'basic' | 'premium' | 'enterprise';
  subscriptionExpiresAt?: Timestamp;
  
  // Limits
  monthlyOrderLimit?: number;
  currentMonthOrders: number;
  
  // Integrations
  integrations?: {
    whatsapp?: {
      enabled: boolean;
      phoneNumber?: string;
      apiKey?: string;
    };
    sms?: {
      enabled: boolean;
      provider?: string;
      apiKey?: string;
    };
    email?: {
      enabled: boolean;
      smtpSettings?: {
        host: string;
        port: number;
        username: string;
        password: string;
      };
    };
  };
  
  // Notification Settings
  notificationSettings: {
    orderCreated: boolean;
    orderStatusChanged: boolean;
    orderDelivered: boolean;
    orderReturned: boolean;
    dailyReport: boolean;
    weeklyReport: boolean;
    monthlyReport: boolean;
  };
  
  // Financial
  accountBalance: number;
  creditLimit?: number;
  
  // Users associated with this company
  userIds: string[];
  
  // Custom fields
  customFields?: Record<string, any>;
}

export interface CompanySettings {
  id: string;
  companyId: string;
  
  // Order Settings
  orderNumberPrefix: string;
  orderNumberFormat: string;
  autoAssignOrders: boolean;
  maxDeliveryAttempts: number;
  defaultOrderPriority: 'low' | 'normal' | 'high' | 'urgent';
  
  // Delivery Settings
  workingHours: {
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
  workingDays: number[]; // 0-6 (Sunday-Saturday)
  deliveryZones: string[];
  
  // Notification Templates
  notificationTemplates: {
    orderConfirmation: string;
    orderDispatched: string;
    orderDelivered: string;
    orderReturned: string;
    orderCancelled: string;
  };
  
  // Branding
  invoiceTemplate?: string;
  receiptTemplate?: string;
  
  // API Settings
  webhookUrl?: string;
  apiKeys: {
    public: string;
    private: string;
  };
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CompanyStats {
  id: string;
  companyId: string;
  
  // Order Statistics
  totalOrders: number;
  pendingOrders: number;
  confirmedOrders: number;
  dispatchedOrders: number;
  deliveredOrders: number;
  returnedOrders: number;
  cancelledOrders: number;
  
  // Financial Statistics
  totalRevenue: number;
  monthlyRevenue: number;
  averageOrderValue: number;
  
  // Performance Statistics
  deliverySuccessRate: number;
  averageDeliveryTime: number; // in hours
  customerSatisfactionRate: number;
  
  // Time-based statistics
  todayOrders: number;
  weekOrders: number;
  monthOrders: number;
  
  lastUpdated: Timestamp;
}

export const BUSINESS_TYPES = [
  'تجارة إلكترونية',
  'مطعم',
  'صيدلية',
  'متجر ملابس',
  'متجر إلكترونيات',
  'متجر كتب',
  'متجر هدايا',
  'خدمات توصيل',
  'أخرى'
];

export const SUBSCRIPTION_PLANS = [
  {
    key: 'basic',
    name: 'الخطة الأساسية',
    monthlyOrderLimit: 100,
    features: ['إدارة الطلبات', 'تتبع التوصيل', 'تقارير أساسية']
  },
  {
    key: 'premium',
    name: 'الخطة المتقدمة',
    monthlyOrderLimit: 500,
    features: ['جميع مميزات الأساسية', 'تقارير متقدمة', 'تكامل API', 'دعم فني']
  },
  {
    key: 'enterprise',
    name: 'خطة المؤسسات',
    monthlyOrderLimit: null, // Unlimited
    features: ['جميع المميزات', 'تخصيص كامل', 'دعم مخصص', 'تدريب']
  }
];
