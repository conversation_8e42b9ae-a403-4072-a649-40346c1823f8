import type { User } from "@/types";
import { MockAuth } from "./mock-auth";

export async function getUsers(filters?: {
  role?: string | string[];
  isActive?: boolean;
  centerId?: string;
  branchId?: string;
  creatorId?: string;
}): Promise<User[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  let users = MockAuth.getAllUsers();
  
  if (filters?.role) {
    if (Array.isArray(filters.role)) {
      users = users.filter(u => filters.role!.includes(u.role));
    } else {
      users = users.filter(u => u.role === filters.role);
    }
  }
  
  if (filters?.isActive !== undefined) {
    users = users.filter(u => u.isActive === filters.isActive);
  }
  
  if (filters?.centerId) {
    users = users.filter(u => u.centerId === filters.centerId);
  }
  
  if (filters?.branchId) {
    users = users.filter(u => u.branchId === filters.branchId);
  }
  
  if (filters?.creatorId) {
    users = users.filter(u => u.creatorId === filters.creatorId);
  }
  
  return users;
}

export async function getUser(id: string): Promise<User | null> {
  await new Promise(resolve => setTimeout(resolve, 300));
  const users = MockAuth.getAllUsers();
  return users.find(u => u.id === id) || null;
}

export async function getUserByEmail(email: string): Promise<User | null> {
  await new Promise(resolve => setTimeout(resolve, 300));
  const users = MockAuth.getAllUsers();
  return users.find(u => u.email === email) || null;
}

export async function createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
  await new Promise(resolve => setTimeout(resolve, 800));
  return await MockAuth.addUser({
    ...userData,
    isActive: userData.isActive ?? true,
  });
}

export async function updateUser(id: string, updates: Partial<User>): Promise<User | null> {
  await new Promise(resolve => setTimeout(resolve, 600));
  return await MockAuth.updateUserInMockData(id, updates);
}

export async function deleteUserById(id: string): Promise<boolean> {
  await new Promise(resolve => setTimeout(resolve, 400));
  return await MockAuth.deleteUser(id);
}
