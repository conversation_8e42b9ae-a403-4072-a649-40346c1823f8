import type { User } from "@/types";

// Mock users data
const mockUsers: User[] = [
  {
    id: '1',
    name: 'أحمد محمد',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    phoneNumber: '0501234567'
  },
  {
    id: '2',
    name: 'فاطمة علي',
    username: 'manager',
    email: '<EMAIL>',
    role: 'manager',
    isActive: true,
    createdAt: new Date('2024-01-02'),
    phoneNumber: '0507654321'
  },
  {
    id: '3',
    name: 'محمد سالم',
    username: 'employee',
    email: '<EMAIL>',
    role: 'employee',
    isActive: true,
    createdAt: new Date('2024-01-03'),
    phoneNumber: '0509876543'
  },
  {
    id: '4',
    name: 'خا<PERSON><PERSON> أحمد',
    username: 'rep1',
    email: '<EMAIL>',
    role: 'مندوب',
    isActive: true,
    createdAt: new Date('2024-01-04'),
    phoneNumber: '0501111111'
  },
  {
    id: '5',
    name: 'سارة محمد',
    username: 'rep2',
    email: '<EMAIL>',
    role: 'مندوب',
    isActive: true,
    createdAt: new Date('2024-01-05'),
    phoneNumber: '0502222222'
  },
  {
    id: '6',
    name: 'عبدالله علي',
    username: 'rep3',
    email: '<EMAIL>',
    role: 'مندوب',
    isActive: true,
    createdAt: new Date('2024-01-06'),
    phoneNumber: '0503333333'
  }
];

export async function getUsers(): Promise<User[]> {
  try {
    // Try to get from Firebase first
    const { UserService } = await import('@/lib/services/user-service');
    const firebaseUsers = await UserService.getUsers();

    // Convert Firebase users to legacy format
    return firebaseUsers.map(user => ({
      ...user,
      createdAt: user.createdAt.toDate(),
      lastLogin: user.lastLogin?.toDate(),
    }));
  } catch (error) {
    console.warn('Firebase not available, using mock data:', error);
    // Fallback to mock data
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockUsers;
  }
}

export async function getUser(id: string): Promise<User | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const user = mockUsers.find(u => u.id === id);
  return user || null;
}

export async function getUserByEmail(email: string): Promise<User | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const user = mockUsers.find(u => u.email === email);
  return user || null;
}

export async function createUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<User> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const newUser: User = {
    ...userData,
    id: Math.random().toString(36).substr(2, 9),
    createdAt: new Date(),
  };
  
  mockUsers.push(newUser);
  return newUser;
}

export async function updateUser(id: string, updates: Partial<User>): Promise<User | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const userIndex = mockUsers.findIndex(u => u.id === id);
  if (userIndex === -1) return null;
  
  mockUsers[userIndex] = {
    ...mockUsers[userIndex],
    ...updates,
  };
  
  return mockUsers[userIndex];
}
