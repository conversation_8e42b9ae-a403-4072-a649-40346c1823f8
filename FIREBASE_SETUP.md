# دليل إعداد Firebase لتطبيق Rihla Delivery

## المتطلبات الأساسية

1. **حساب Google/Firebase**
2. **Node.js 18+**
3. **Firebase CLI**

## خطوات الإعداد

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع: `rihla-delivery`
4. اختر إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء المشروع"

### 2. إعداد Firestore Database

1. في Firebase Console، اذهب إلى "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر موقع قاعدة البيانات (مثل: europe-west3)

### 3. إعد<PERSON> Authentication

1. اذهب إلى "Authentication"
2. انقر على "البدء" أو "Get started"
3. في تبويب "Sign-in method"
4. فعّل "Email/Password"

### 4. إعداد Storage (اختياري)

1. اذهب إلى "Storage"
2. انقر على "البدء"
3. اختر قواعد الأمان المناسبة

### 5. الحصول على إعدادات المشروع

1. اذهب إلى "Project Settings" (أيقونة الترس)
2. في تبويب "General"
3. انزل إلى "Your apps"
4. انقر على "Add app" واختر "Web"
5. أدخل اسم التطبيق: `rihla-delivery-web`
6. انسخ إعدادات Firebase

### 6. إعداد متغيرات البيئة

1. انسخ ملف `.env.local.example` إلى `.env.local`
2. املأ المتغيرات بإعدادات Firebase:

```bash
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456
```

### 7. تثبيت Firebase CLI

```bash
npm install -g firebase-tools
```

### 8. تسجيل الدخول إلى Firebase

```bash
firebase login
```

### 9. ربط المشروع المحلي بـ Firebase

```bash
firebase init
```

اختر:
- Firestore
- Hosting (اختياري)
- Storage (اختياري)

### 10. رفع قواعد الأمان

```bash
firebase deploy --only firestore:rules
```

### 11. رفع الفهارس

```bash
firebase deploy --only firestore:indexes
```

## تشغيل التطبيق

### للتطوير المحلي

```bash
# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm run dev
```

### مع Firebase Emulators (للتطوير)

```bash
# تشغيل Firebase Emulators
npm run firebase:emulators

# في terminal آخر، تشغيل التطبيق
npm run dev
```

### للإنتاج

```bash
# بناء التطبيق
npm run build

# رفع إلى Firebase Hosting
npm run firebase:deploy
```

## إنشاء المستخدمين الأوائل

بعد تشغيل التطبيق لأول مرة، سيتم إنشاء البيانات التجريبية تلقائياً.

للوصول إلى التطبيق، استخدم:

- **المدير**: <EMAIL> / password
- **المشرف**: <EMAIL> / password
- **الموظف**: <EMAIL> / password

## إعداد قواعد الأمان المتقدمة

بعد إنشاء المستخدمين، يمكنك تحديث قواعد Firestore في ملف `firestore.rules` ثم رفعها:

```bash
firebase deploy --only firestore:rules
```

## استكشاف الأخطاء

### مشكلة في الاتصال بـ Firebase

1. تأكد من صحة متغيرات البيئة
2. تأكد من تفعيل Authentication و Firestore
3. تحقق من قواعد الأمان

### مشكلة في البيانات

1. تحقق من Firebase Console
2. تأكد من وجود البيانات في Firestore
3. تحقق من الفهارس

### مشكلة في التطبيق المحمول

1. تأكد من إعداد Capacitor بشكل صحيح
2. تحقق من أن Firebase يعمل على الويب أولاً
3. تأكد من إعدادات CORS

## الميزات المتقدمة

### Cloud Functions (اختياري)

لإضافة Cloud Functions:

```bash
firebase init functions
```

### Push Notifications (اختياري)

لإضافة الإشعارات:

1. فعّل Cloud Messaging في Firebase Console
2. أضف Service Worker للويب
3. أعد إعداد Capacitor للموبايل

### Analytics (اختياري)

لإضافة Google Analytics:

1. فعّل Analytics في Firebase Console
2. أضف SDK في التطبيق

## الأمان والإنتاج

### قواعد الأمان

- راجع ملف `firestore.rules`
- تأكد من تقييد الوصول حسب الأدوار
- اختبر القواعد باستخدام Firebase Emulator

### النسخ الاحتياطي

- فعّل النسخ الاحتياطي التلقائي في Firebase Console
- أعد إعداد تصدير البيانات دورياً

### المراقبة

- فعّل Performance Monitoring
- أعد إعداد Alerts للأخطاء
- راقب الاستخدام والتكاليف

## الدعم

للحصول على المساعدة:

1. راجع [وثائق Firebase](https://firebase.google.com/docs)
2. تحقق من [Firebase Status](https://status.firebase.google.com/)
3. ابحث في [Stack Overflow](https://stackoverflow.com/questions/tagged/firebase)
