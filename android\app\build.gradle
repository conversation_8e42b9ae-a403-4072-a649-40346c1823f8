apply plugin: 'com.android.application'

android {
    namespace 'com.rihla.delivery'
    compileSdkVersion rootProject.ext.compileSdkVersion
    defaultConfig {
        applicationId "com.rihla.delivery"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }
    
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            
            // Signing config for release builds
            signingConfig signingConfigs.debug // Change this for production
            
            // Optimization settings
            shrinkResources false
            zipAlignEnabled true
            
            // Build config fields
            buildConfigField "String", "BUILD_TYPE", '"release"'
            buildConfigField "boolean", "DEBUG_MODE", "false"
        }
        
        debug {
            debuggable true
            minifyEnabled false
            
            // Build config fields
            buildConfigField "String", "BUILD_TYPE", '"debug"'
            buildConfigField "boolean", "DEBUG_MODE", "true"
            
            // Application ID suffix for debug builds
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    // Packaging options
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }
    
    // Lint options
    lintOptions {
        abortOnError false
        checkReleaseBuilds false
        disable 'MissingTranslation'
    }
    
    // Data binding (if needed)
    dataBinding {
        enabled = false
    }
    
    // View binding (if needed)
    viewBinding {
        enabled = false
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
    
    // Additional dependencies for enhanced functionality
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.google.android.material:material:1.9.0'
    
    // Network and HTTP
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    
    // Image loading
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    
    // JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // Permissions
    implementation 'pub.devrel:easypermissions:3.0.0'
    
    // Firebase (optional - uncomment if using Firebase)
    // implementation platform('com.google.firebase:firebase-bom:32.2.2')
    // implementation 'com.google.firebase:firebase-analytics'
    // implementation 'com.google.firebase:firebase-messaging'
    // implementation 'com.google.firebase:firebase-firestore'
    // implementation 'com.google.firebase:firebase-auth'
    // implementation 'com.google.firebase:firebase-storage'
    
    // Capacitor plugins
    implementation 'com.capacitorjs:camera:5.0.7'
    implementation 'com.capacitorjs:filesystem:5.1.4'
    implementation 'com.capacitorjs:geolocation:5.0.6'
    implementation 'com.capacitorjs:network:5.0.6'
    implementation 'com.capacitorjs:share:5.0.6'
    implementation 'com.capacitorjs:splash-screen:5.0.6'
    implementation 'com.capacitorjs:status-bar:5.0.6'
    implementation 'com.capacitorjs:toast:5.0.6'
    implementation 'com.capacitorjs:device:5.0.6'
    implementation 'com.capacitorjs:app:5.0.6'
    implementation 'com.capacitorjs:haptics:5.0.6'
    implementation 'com.capacitorjs:keyboard:5.0.6'
    implementation 'com.capacitorjs:preferences:5.0.6'
    implementation 'com.capacitorjs:push-notifications:5.0.6'
}

// Apply Google Services plugin (uncomment if using Firebase)
// apply plugin: 'com.google.gms.google-services'

// Task to copy web assets
task copyWebAssets(type: Copy) {
    from '../../out'
    into 'src/main/assets/public'
}

// Make sure web assets are copied before building
preBuild.dependsOn copyWebAssets

// Custom tasks for build automation
task generateBuildInfo {
    doLast {
        def buildInfoFile = file('src/main/assets/build-info.json')
        buildInfoFile.parentFile.mkdirs()
        buildInfoFile.text = """
{
    "buildTime": "${new Date().format('yyyy-MM-dd HH:mm:ss')}",
    "buildType": "${android.defaultConfig.versionName}",
    "versionCode": ${android.defaultConfig.versionCode},
    "versionName": "${android.defaultConfig.versionName}",
    "applicationId": "${android.defaultConfig.applicationId}"
}
"""
    }
}

// Run build info generation before building
preBuild.dependsOn generateBuildInfo

// Clean task enhancement
clean {
    delete 'src/main/assets/public'
    delete 'src/main/assets/build-info.json'
}
