"use client";

import React, { useState, useMemo, useEffect, useC<PERSON>back } from "react";
import { PageHeader } from "@/components/page-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle, Edit, Trash2, Settings2, Filter, Users as UsersIcon, Building, Briefcase, ArrowRight, Home, UserCheck, UserCog, UserRoundSearch, UserX, ChevronLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import type { User, UserRole } from "@/types";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/auth-context";
import { getUsers } from "@/lib/user-data";

// Mock data for demo
const allUserRoles: UserRole[] = ["admin", "manager", "employee", "مندوب", "عميل", "متابع"];

const getRolesForCreator = (role: UserRole): UserRole[] => {
  switch (role) {
    case "admin":
      return ["manager", "employee", "مندوب", "عميل", "متابع"];
    case "manager":
      return ["employee", "مندوب", "عميل", "متابع"];
    case "employee":
      return ["مندوب"];
    default:
      return [];
  }
};

type ViewState = 
  | { level: 'all' }
  | { level: 'center_branches', centerId: string, centerName: string }
  | { level: 'branch_employees', branchId: string, branchName: string, centerId: string };

export default function UsersPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRoleFilter, setSelectedRoleFilter] = useState<string>("all");
  const [canCurrentUserAddUsers, setCanCurrentUserAddUsers] = useState(false);
  const [viewState, setViewState] = useState<ViewState>({ level: 'all' });
  const [users, setUsers] = useState<User[]>([]);
  const { currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  const fetchUsers = useCallback(async () => {
    if (!currentUser) return;
    setIsLoading(true);

    try {
      const allUsers = await getUsers();
      setUsers(allUsers);
    } catch (e) {
      toast({variant: "destructive", title: "خطأ", description: "فشل تحميل قائمة المستخدمين."});
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  }, [currentUser, toast, viewState]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  useEffect(() => {
    if (currentUser) {
      const rolesCreatableByCurrentUser = getRolesForCreator(currentUser.role);
      setCanCurrentUserAddUsers(rolesCreatableByCurrentUser.length > 0);
    }
  }, [currentUser]);

  const getRoleBadgeVariant = (role: User["role"]) => {
    switch (role) {
      case "admin": return "default"; 
      case "manager": return "secondary"; 
      case "employee": return "destructive"; 
      case "مندوب": return "default";
      case "متابع": return "secondary";
      case "عميل": return "secondary";
      default: return "secondary";
    }
  };
  
  const filteredUsers = useMemo(() => {
    return users.filter(user =>
      (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.username && user.username.toLowerCase().includes(searchTerm.toLowerCase()))) &&
      (selectedRoleFilter === "all" || user.role === selectedRoleFilter)
    );
  }, [searchTerm, selectedRoleFilter, users]);
  
  const handleDeleteUser = async (userIdToDelete: string) => {
    const userToDelete = users.find(u => u.id === userIdToDelete);
    if (!currentUser || !userToDelete) return;
    
    const rolesDeleterCanCreate = getRolesForCreator(currentUser.role);
    if (currentUser.role !== 'admin' && !rolesDeleterCanCreate.includes(userToDelete.role)) {
        toast({ variant: "destructive", title: "غير مصرح", description: "ليس لديك الصلاحية لحذف هذا المستخدم." });
        return;
    }
    if (userToDelete.id === currentUser.id) {
         toast({ variant: "destructive", title: "غير مصرح", description: "لا يمكنك حذف حسابك الخاص." });
        return;
    }
    
    // Mock deletion
    setUsers(prev => prev.filter(u => u.id !== userIdToDelete));
    toast({ title: "تم الحذف", description: `تم حذف المستخدم ${userToDelete.name} بنجاح.`});
  };

  const renderUserCard = (user: User) => (
    <Card 
        key={user.id} 
        className="shadow-md hover:shadow-lg transition-shadow flex flex-col cursor-pointer border-2 border-primary"
    >
      <CardHeader className="pb-3 text-center">
        <Avatar className="h-20 w-20 mx-auto mb-3 border-2 border-primary">
          <AvatarImage src={`https://placehold.co/80x80.png?text=${user.name.substring(0,1)}`} alt={user.name} />
          <AvatarFallback className="text-2xl">{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
        </Avatar>
        <CardTitle className="text-lg">{user.name}</CardTitle>
        {user.username && <CardDescription>@{user.username}</CardDescription>}
      </CardHeader>
      <CardContent className="text-sm space-y-1.5 flex-grow">
        <div className="flex items-center gap-2">
          <Briefcase className="h-4 w-4 text-muted-foreground" />
          <Badge variant={getRoleBadgeVariant(user.role)}>{user.role}</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4 text-muted-foreground" />
          <span>العراق - بغداد</span>
        </div>
      </CardContent>
      <CardFooter className="pt-3 pb-3 px-4 border-t">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="w-full">
              <Settings2 className="ml-2 h-4 w-4" />
              إجراءات
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => toast({title: "قيد التطوير", description: "سيتم تفعيل تعديل الصلاحيات قريباً."})}>
              <Edit className="ml-2 h-4 w-4" />
              تعديل بيانات/صلاحيات
            </DropdownMenuItem>
            {user.id !== currentUser?.id && ( 
                <DropdownMenuItem className="text-destructive focus:text-destructive focus:bg-destructive/10" onClick={(e) => {e.stopPropagation(); handleDeleteUser(user.id)}}>
                <Trash2 className="ml-2 h-4 w-4" />
                حذف المستخدم
                </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );

  const renderContent = () => {
    if (isLoading) {
      return <div className="text-center py-10 text-muted-foreground flex justify-center items-center gap-2"><Loader2 className="h-8 w-8 animate-spin"/> جار تحميل المستخدمين...</div>;
    }
    
    if (filteredUsers.length === 0) {
      return <div className="text-center py-10 text-muted-foreground">لا يوجد مستخدمون يطابقون الفلترة الحالية.</div>;
    }
    
    return <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">{filteredUsers.map(renderUserCard)}</div>;
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="إدارة الموظفين"
        description="عرض وإدارة الموظفين والعملاء"
        actions={
          <div className="flex flex-wrap gap-2 items-center">
             <Button variant="outline" onClick={() => router.back()}>
                <ArrowRight className="h-4 w-4" />
                رجوع
             </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
            {canCurrentUserAddUsers && (
                <Button asChild>
                    <Link href="/users/new">
                        <PlusCircle className="h-4 w-4" />
                        إضافة مستخدم جديد
                    </Link>
                </Button>
            )}
          </div>
        }
      />
      <Card className="shadow-lg">
        <CardHeader>
          <div className="pt-2 grid grid-cols-1 md:grid-cols-2 gap-2 items-center">
            <Input 
              placeholder="بحث بالاسم أو اسم المستخدم..." 
              className="flex-1"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={selectedRoleFilter} onValueChange={setSelectedRoleFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="اختر دور الموظف للفلترة..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأدوار</SelectItem>
                  {allUserRoles.map(role => (
                    <SelectItem key={role} value={role}>{role}</SelectItem>
                  ))}
                </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
}
