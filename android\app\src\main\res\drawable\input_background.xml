<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/input_background" />
            <stroke android:width="2dp" android:color="@color/input_border_focus" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/gray_100" />
            <stroke android:width="1dp" android:color="@color/gray_300" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/input_background" />
            <stroke android:width="1dp" android:color="@color/input_border" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
