"use client";

import React from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Building, History, ArrowRight, Home } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";

export default function AccountingHubPage() {
  const router = useRouter();
  const { currentUser } = useAuth();

  const accountingSections = [
    {
      title: "محاسبة المندوبين",
      description: "إدارة محاسبة المندوبين بناءً على الطلبات المسلمة والعمولات.",
      href: "/accounting/agents",
      icon: Users,
      iconColor: "text-blue-500",
      isAllowed: true, // Allow all users for demo
    },
    {
      title: "أرشيف المحاسبات",
      description: "عرض سجلات المحاسبة والتسويات المالية المؤرشفة.",
      href: "/accounting/archive",
      icon: History,
      iconColor: "text-gray-500",
      isAllowed: true, // Allow all users for demo
    },
  ];

  const visibleSections = accountingSections.filter(s => s.isAllowed);

  return (
    <div className="container mx-auto px-4 py-6">
      <PageHeader
        title="المحاسبة المالية"
        description="اختر نوع عملية المحاسبة التي ترغب بإدارتها."
        actions={
          <div className="flex flex-wrap gap-2 items-center">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowRight className="h-4 w-4" />
              رجوع
            </Button>
            <Button asChild variant="outline">
              <Link href="/">
                <Home className="h-4 w-4" />
                الرئيسية
              </Link>
            </Button>
          </div>
        }
      />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {visibleSections.map((section) => (
          <Link href={section.href} key={section.href} passHref>
            <Card className="shadow-lg hover:shadow-xl transition-shadow h-full flex flex-col items-center justify-center text-center p-6 cursor-pointer border-2 border-primary">
              <section.icon className={`h-12 w-12 ${section.iconColor} mb-3`} />
              <CardTitle>{section.title}</CardTitle>
              <CardDescription>{section.description}</CardDescription>
            </Card>
          </Link>
        ))}
        {visibleSections.length === 0 && (
            <p className="text-muted-foreground col-span-full text-center py-8">ليس لديك صلاحية الوصول لأي قسم من أقسام المحاسبة.</p>
        )}
      </div>
    </div>
  );
}
