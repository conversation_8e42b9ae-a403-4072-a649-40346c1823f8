@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo   🔍 فحص إعداد Android للتطوير
echo ========================================
echo.

set "errors=0"
set "warnings=0"

:: Check Node.js
echo 📦 فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    set /a errors+=1
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set "node_version=%%i"
    echo ✅ Node.js: !node_version!
)

:: Check npm
echo 📦 فحص npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    set /a errors+=1
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set "npm_version=%%i"
    echo ✅ npm: !npm_version!
)

:: Check Java
echo ☕ فحص Java...
where java >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت
    set /a errors+=1
) else (
    for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do set "java_version=%%i"
    echo ✅ Java: !java_version!
)

:: Check JAVA_HOME
echo 🏠 فحص JAVA_HOME...
if "%JAVA_HOME%"=="" (
    echo ⚠️ JAVA_HOME غير محدد
    set /a warnings+=1
) else (
    echo ✅ JAVA_HOME: %JAVA_HOME%
)

:: Check Android SDK
echo 📱 فحص Android SDK...
if "%ANDROID_HOME%"=="" (
    if "%ANDROID_SDK_ROOT%"=="" (
        echo ❌ ANDROID_HOME أو ANDROID_SDK_ROOT غير محدد
        set /a errors+=1
    ) else (
        echo ✅ ANDROID_SDK_ROOT: %ANDROID_SDK_ROOT%
    )
) else (
    echo ✅ ANDROID_HOME: %ANDROID_HOME%
)

:: Check adb
echo 🔧 فحص ADB...
where adb >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADB غير متاح في PATH
    set /a errors+=1
) else (
    for /f "tokens=*" %%i in ('adb version 2^>nul ^| findstr "Android Debug Bridge"') do set "adb_version=%%i"
    echo ✅ ADB: !adb_version!
)

:: Check Gradle
echo 🏗️ فحص Gradle...
where gradle >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Gradle غير متاح في PATH (سيتم استخدام Gradle Wrapper)
    set /a warnings+=1
) else (
    for /f "tokens=*" %%i in ('gradle --version 2^>nul ^| findstr "Gradle"') do set "gradle_version=%%i"
    echo ✅ Gradle: !gradle_version!
)

:: Check Android Studio
echo 🎨 فحص Android Studio...
set "studio_found=0"
if exist "C:\Program Files\Android\Android Studio\bin\studio64.exe" (
    echo ✅ Android Studio: C:\Program Files\Android\Android Studio\
    set "studio_found=1"
) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\tools\android.bat" (
    echo ✅ Android SDK Tools متاح
    set "studio_found=1"
) else (
    echo ❌ Android Studio غير موجود في المكان المتوقع
    set /a errors+=1
)

:: Check Capacitor CLI
echo ⚡ فحص Capacitor CLI...
where cap >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Capacitor CLI غير مثبت عالمياً (سيتم استخدام npx)
    set /a warnings+=1
) else (
    for /f "tokens=*" %%i in ('cap --version 2^>nul') do set "cap_version=%%i"
    echo ✅ Capacitor CLI: !cap_version!
)

:: Check project files
echo 📁 فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ package.json غير موجود
    set /a errors+=1
) else (
    echo ✅ package.json موجود
)

if not exist "capacitor.config.ts" (
    if not exist "capacitor.config.js" (
        echo ❌ capacitor.config.ts/js غير موجود
        set /a errors+=1
    ) else (
        echo ✅ capacitor.config.js موجود
    )
) else (
    echo ✅ capacitor.config.ts موجود
)

if not exist "android" (
    echo ❌ مجلد android غير موجود
    set /a errors+=1
) else (
    echo ✅ مجلد android موجود
)

if exist "android\app\build.gradle" (
    echo ✅ android/app/build.gradle موجود
) else (
    echo ❌ android/app/build.gradle غير موجود
    set /a errors+=1
)

:: Check connected devices
echo 📱 فحص الأجهزة المتصلة...
where adb >nul 2>&1
if %errorlevel% equ 0 (
    for /f "skip=1 tokens=*" %%i in ('adb devices 2^>nul') do (
        if not "%%i"=="" (
            echo ✅ جهاز متصل: %%i
        )
    )
) else (
    echo ⚠️ لا يمكن فحص الأجهزة (ADB غير متاح)
)

:: Check disk space
echo 💾 فحص مساحة القرص...
for /f "tokens=3" %%i in ('dir /-c ^| findstr "bytes free"') do set "free_space=%%i"
if defined free_space (
    echo ✅ المساحة المتاحة: !free_space! bytes
) else (
    echo ⚠️ لا يمكن تحديد المساحة المتاحة
)

:: Summary
echo.
echo ========================================
echo           📊 ملخص الفحص
echo ========================================
echo.

if %errors% equ 0 (
    if %warnings% equ 0 (
        echo ✅ جميع المتطلبات متوفرة! يمكنك بناء التطبيق الآن.
        echo.
        echo 🚀 للبدء:
        echo    android-build.bat
    ) else (
        echo ⚠️ الإعداد جيد مع !warnings! تحذير
        echo.
        echo 💡 التحذيرات لا تمنع البناء ولكن قد تحسن التجربة
    )
) else (
    echo ❌ يوجد !errors! خطأ يجب إصلاحه قبل البناء
    echo.
    echo 🔧 خطوات الإصلاح:
    
    if %errors% gtr 0 (
        echo.
        echo 📋 قائمة المتطلبات:
        echo 1. Node.js 18+ - https://nodejs.org
        echo 2. Java JDK 8+ - https://adoptium.net
        echo 3. Android Studio - https://developer.android.com/studio
        echo 4. إعداد متغيرات البيئة:
        echo    - JAVA_HOME
        echo    - ANDROID_HOME أو ANDROID_SDK_ROOT
        echo    - إضافة أدوات Android SDK إلى PATH
        echo.
        echo 📖 للمساعدة التفصيلية:
        echo    - ANDROID_SETUP.md
        echo    - ANDROID_QUICK_START.md
    )
)

echo.
echo ========================================
echo.

if %errors% equ 0 (
    echo 🎯 الخطوة التالية: تشغيل android-build.bat
    echo.
    set /p run_build="هل تريد تشغيل البناء الآن؟ (y/n): "
    if /i "!run_build!"=="y" (
        echo.
        echo 🚀 تشغيل البناء...
        call android-build.bat
    )
) else (
    echo 🔧 يرجى إصلاح الأخطاء أولاً ثم إعادة تشغيل الفحص
)

echo.
pause
exit /b %errors%
