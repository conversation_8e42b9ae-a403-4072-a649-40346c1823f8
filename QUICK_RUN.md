# 🚀 تشغيل سريع - Rihla Delivery

## ⚡ التشغيل السريع

### **الطريقة الأسهل:**
```bash
# Windows
start-app.bat

# أو
npm run dev
```

## 🌐 الوصول للتطبيق

**الرابط:** http://localhost:3000

## 🔑 بيانات الدخول السريعة

### **🏢 الإدارة العليا:**
- **المدير العام**: `admin` / `password`
- **المشرف**: `manager` / `password`
- **الموظف**: `employee` / `password`

### **🏬 إدارة المراكز والفروع:**
- **مدير المركز**: `center1` / `password`
- **مدير الفرع**: `branch1` / `password`

### **🚚 المندوبين:**
- **المندوب الأول**: `rep1` / `password`
- **المندوب الثاني**: `rep2` / `password`
- **المندوب الثالث**: `rep3` / `password`

### **💼 الأدوار الأخرى:**
- **المحاسب**: `accountant1` / `password`
- **المرسل**: `dispatcher1` / `password`

### **⭐ الحساب الافتراضي:**
- **default** / `123456`

## 🎯 الميزات المتاحة

### **✅ الميزات الأساسية:**
- تسجيل الدخول والخروج
- لوحة التحكم
- إدارة الطلبات
- إدارة المستخدمين
- محاسبة المندوبين
- أرشيف الطلبات والمحاسبات
- التقارير والإحصائيات

### **✅ الميزات المتقدمة:**
- إسناد الطلبات للمندوبين
- إسناد الطلبات للفروع والمراكز
- إدارة الرواجع
- استيراد/تصدير Excel
- طباعة الكشوفات والوصولات
- البحث والفلترة المتقدمة
- واجهة عربية متجاوبة
- دعم الوضع المظلم

## 📱 تشغيل على الأندرويد

### **فحص الإعداد:**
```bash
check-android-setup.bat
```

### **بناء التطبيق:**
```bash
android-build.bat
```

### **الخيارات المتاحة:**
1. بناء سريع
2. بناء وتشغيل
3. بناء APK للإنتاج
4. تنظيف وإعادة بناء
5. مزامنة Capacitor
6. فتح Android Studio

## 🛠️ حل المشاكل السريع

### **مشكلة: التطبيق لا يعمل**
```bash
npm install
npm run dev
```

### **مشكلة: خطأ في التبعيات**
```bash
rm -rf node_modules
rm package-lock.json
npm install
```

### **مشكلة: خطأ في Next.js**
```bash
rm -rf .next
npm run build
npm run dev
```

## 📚 المراجع السريعة

### **الملفات المفيدة:**
- `LOGIN_ACCOUNTS.md` - جميع حسابات الدخول
- `ANDROID_QUICK_START.md` - دليل الأندرويد السريع
- `README.md` - الدليل الشامل

### **ملفات التشغيل:**
- `start-app.bat` - تشغيل سريع للويب
- `android-build.bat` - بناء الأندرويد
- `check-android-setup.bat` - فحص إعداد الأندرويد

## 🎮 اختبار سريع

### **1. تسجيل الدخول:**
- افتح http://localhost:3000
- استخدم `admin` / `password`

### **2. إضافة طلب:**
- اذهب إلى "إدارة الطلبات"
- انقر "إضافة طلب جديد"

### **3. إسناد طلب:**
- اذهب إلى "إسناد الطلبات"
- اختر مندوب وطلبات

### **4. محاسبة مندوب:**
- اذهب إلى "المحاسبة المالية"
- اختر "محاسبة المندوبين"

## 🆘 المساعدة السريعة

### **إذا واجهت مشكلة:**
1. تأكد من تثبيت Node.js 18+
2. تأكد من تشغيل `npm install`
3. تأكد من الرابط: http://localhost:3000
4. جرب إعادة تشغيل التطبيق

### **للحصول على مساعدة مفصلة:**
- راجع `README.md`
- راجع `ANDROID_SETUP.md`
- راجع `LOGIN_ACCOUNTS.md`

---

**نصيحة:** استخدم `start-app.bat` للتشغيل السريع! 🚀
