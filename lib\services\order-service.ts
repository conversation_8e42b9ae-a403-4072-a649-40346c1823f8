import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  Timestamp,
  serverTimestamp,
  writeBatch,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { FirebaseOrder, OrderStatus, OrderStatusHistory, DeliveryAttempt } from '@/lib/models/order';

const ORDERS_COLLECTION = 'orders';
const ORDER_HISTORY_COLLECTION = 'orderHistory';
const DELIVERY_ATTEMPTS_COLLECTION = 'deliveryAttempts';

export interface OrderFilters {
  status?: OrderStatus;
  agentId?: string;
  companyId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  searchTerm?: string;
  priority?: string;
}

export interface PaginationOptions {
  pageSize?: number;
  lastDoc?: any;
}

export class OrderService {
  // Get orders with filters and pagination
  static async getOrders(
    filters: OrderFilters = {}, 
    pagination: PaginationOptions = {}
  ): Promise<{ orders: FirebaseOrder[], hasMore: boolean, lastDoc: any }> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      let q = query(ordersRef, orderBy('createdAt', 'desc'));

      // Apply filters
      if (filters.status) {
        q = query(q, where('status', '==', filters.status));
      }
      if (filters.agentId) {
        q = query(q, where('agentId', '==', filters.agentId));
      }
      if (filters.companyId) {
        q = query(q, where('companyId', '==', filters.companyId));
      }
      if (filters.dateFrom) {
        q = query(q, where('createdAt', '>=', Timestamp.fromDate(filters.dateFrom)));
      }
      if (filters.dateTo) {
        q = query(q, where('createdAt', '<=', Timestamp.fromDate(filters.dateTo)));
      }

      // Apply pagination
      const pageSize = pagination.pageSize || 20;
      q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

      if (pagination.lastDoc) {
        q = query(q, startAfter(pagination.lastDoc));
      }

      const snapshot = await getDocs(q);
      const orders = snapshot.docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      } as FirebaseOrder));

      const hasMore = snapshot.docs.length > pageSize;
      const lastDoc = hasMore ? snapshot.docs[pageSize - 1] : null;

      return { orders, hasMore, lastDoc };
    } catch (error) {
      console.error('Error getting orders:', error);
      throw error;
    }
  }

  // Get order by ID
  static async getOrder(orderId: string): Promise<FirebaseOrder | null> {
    try {
      const orderRef = doc(db, ORDERS_COLLECTION, orderId);
      const orderSnap = await getDoc(orderRef);
      
      if (orderSnap.exists()) {
        return {
          id: orderSnap.id,
          ...orderSnap.data()
        } as FirebaseOrder;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting order:', error);
      throw error;
    }
  }

  // Get order by order number
  static async getOrderByNumber(orderNumber: string): Promise<FirebaseOrder | null> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      const q = query(ordersRef, where('orderNumber', '==', orderNumber), limit(1));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as FirebaseOrder;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting order by number:', error);
      throw error;
    }
  }

  // Search orders
  static async searchOrders(searchTerm: string): Promise<FirebaseOrder[]> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      
      // Search by order number
      const orderNumberQuery = query(
        ordersRef, 
        where('orderNumber', '>=', searchTerm),
        where('orderNumber', '<=', searchTerm + '\uf8ff'),
        limit(10)
      );
      
      // Search by customer phone
      const phoneQuery = query(
        ordersRef,
        where('mobileNumber', '>=', searchTerm),
        where('mobileNumber', '<=', searchTerm + '\uf8ff'),
        limit(10)
      );

      const [orderNumberSnapshot, phoneSnapshot] = await Promise.all([
        getDocs(orderNumberQuery),
        getDocs(phoneQuery)
      ]);

      const orderNumberResults = orderNumberSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as FirebaseOrder));

      const phoneResults = phoneSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as FirebaseOrder));

      // Combine and deduplicate results
      const allResults = [...orderNumberResults, ...phoneResults];
      const uniqueResults = allResults.filter((order, index, self) => 
        index === self.findIndex(o => o.id === order.id)
      );

      return uniqueResults;
    } catch (error) {
      console.error('Error searching orders:', error);
      throw error;
    }
  }

  // Create new order
  static async createOrder(orderData: Omit<FirebaseOrder, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const newOrder = {
        ...orderData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        deliveryAttempts: 0,
        maxDeliveryAttempts: orderData.maxDeliveryAttempts || 3,
      };

      const docRef = await addDoc(collection(db, ORDERS_COLLECTION), newOrder);
      
      // Create initial status history entry
      await this.addStatusHistory(docRef.id, orderData.status, 'system', 'Order created');
      
      return docRef.id;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Update order
  static async updateOrder(orderId: string, updates: Partial<FirebaseOrder>): Promise<void> {
    try {
      const orderRef = doc(db, ORDERS_COLLECTION, orderId);
      await updateDoc(orderRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }

  // Update order status
  static async updateOrderStatus(
    orderId: string, 
    newStatus: OrderStatus, 
    changedBy: string, 
    notes?: string
  ): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      // Update order
      const orderRef = doc(db, ORDERS_COLLECTION, orderId);
      batch.update(orderRef, {
        status: newStatus,
        updatedAt: serverTimestamp(),
        ...(newStatus === 'delivered' && { deliveredAt: serverTimestamp() })
      });

      // Add status history
      const historyRef = doc(collection(db, ORDER_HISTORY_COLLECTION));
      batch.set(historyRef, {
        orderId,
        status: newStatus,
        changedBy,
        changedAt: serverTimestamp(),
        notes: notes || ''
      });

      await batch.commit();
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Assign order to agent
  static async assignOrder(orderId: string, agentId: string, agentName: string): Promise<void> {
    try {
      const orderRef = doc(db, ORDERS_COLLECTION, orderId);
      await updateDoc(orderRef, {
        agentId,
        agentName,
        updatedAt: serverTimestamp(),
      });

      // Add status history
      await this.addStatusHistory(orderId, 'confirmed', agentId, `Order assigned to ${agentName}`);
    } catch (error) {
      console.error('Error assigning order:', error);
      throw error;
    }
  }

  // Delete orders by criteria
  static async deleteOrdersByCriteria(
    companyId: string,
    status: OrderStatus | 'all',
    dateFrom: Date,
    dateTo: Date
  ): Promise<number> {
    try {
      const ordersRef = collection(db, ORDERS_COLLECTION);
      let q = query(
        ordersRef,
        where('companyId', '==', companyId),
        where('createdAt', '>=', Timestamp.fromDate(dateFrom)),
        where('createdAt', '<=', Timestamp.fromDate(dateTo))
      );

      if (status !== 'all') {
        q = query(q, where('status', '==', status));
      }

      const snapshot = await getDocs(q);
      const batch = writeBatch(db);
      
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      return snapshot.docs.length;
    } catch (error) {
      console.error('Error deleting orders by criteria:', error);
      throw error;
    }
  }

  // Add status history entry
  static async addStatusHistory(
    orderId: string, 
    status: OrderStatus, 
    changedBy: string, 
    notes?: string
  ): Promise<void> {
    try {
      await addDoc(collection(db, ORDER_HISTORY_COLLECTION), {
        orderId,
        status,
        changedBy,
        changedAt: serverTimestamp(),
        notes: notes || ''
      });
    } catch (error) {
      console.error('Error adding status history:', error);
      throw error;
    }
  }

  // Get order status history
  static async getOrderHistory(orderId: string): Promise<OrderStatusHistory[]> {
    try {
      const historyRef = collection(db, ORDER_HISTORY_COLLECTION);
      const q = query(
        historyRef,
        where('orderId', '==', orderId),
        orderBy('changedAt', 'desc')
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as OrderStatusHistory));
    } catch (error) {
      console.error('Error getting order history:', error);
      throw error;
    }
  }

  // Generate unique order number
  static generateOrderNumber(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `ORD-${timestamp}-${random}`.toUpperCase();
  }
}
