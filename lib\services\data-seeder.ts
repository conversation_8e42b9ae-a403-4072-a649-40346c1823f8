import { UserService } from './user-service';
import { CompanyService } from './company-service';
import { OrderService } from './order-service';
import { FirebaseUser, defaultPermissions } from '@/lib/models/user';
import { Company } from '@/lib/models/company';
import { FirebaseOrder } from '@/lib/models/order';
import { Timestamp } from 'firebase/firestore';

export class DataSeeder {
  // Seed all demo data
  static async seedAllData(): Promise<void> {
    try {
      console.log('Starting data seeding...');
      
      // Seed companies first
      const companyIds = await this.seedCompanies();
      console.log('Companies seeded:', companyIds);
      
      // Seed users
      const userIds = await this.seedUsers(companyIds);
      console.log('Users seeded:', userIds);
      
      // Seed orders
      const orderIds = await this.seedOrders(companyIds, userIds);
      console.log('Orders seeded:', orderIds);
      
      console.log('Data seeding completed successfully!');
    } catch (error) {
      console.error('Error seeding data:', error);
      throw error;
    }
  }

  // Seed demo companies
  static async seedCompanies(): Promise<string[]> {
    const companies = [
      {
        name: 'شركة المسرة',
        businessName: 'شركة المسرة للتجارة الإلكترونية',
        email: '<EMAIL>',
        phoneNumber: '0501111111',
        address: 'الرياض، حي النخيل، شارع الملك فهد',
        city: 'الرياض',
        country: 'السعودية',
        businessType: 'تجارة إلكترونية',
        isActive: true,
        defaultDeliveryFee: 15,
        defaultCurrency: 'SAR',
        subscriptionPlan: 'premium' as const,
        notificationSettings: {
          orderCreated: true,
          orderStatusChanged: true,
          orderDelivered: true,
          orderReturned: true,
          dailyReport: true,
          weeklyReport: true,
          monthlyReport: true,
        }
      },
      {
        name: 'شركة البرق',
        businessName: 'شركة البرق للتوصيل السريع',
        email: '<EMAIL>',
        phoneNumber: '0502222222',
        address: 'جدة، حي الصفا، شارع التحلية',
        city: 'جدة',
        country: 'السعودية',
        businessType: 'خدمات توصيل',
        isActive: true,
        defaultDeliveryFee: 20,
        defaultCurrency: 'SAR',
        subscriptionPlan: 'basic' as const,
        notificationSettings: {
          orderCreated: true,
          orderStatusChanged: true,
          orderDelivered: true,
          orderReturned: false,
          dailyReport: false,
          weeklyReport: true,
          monthlyReport: true,
        }
      },
      {
        name: 'شركة الجوهرة',
        businessName: 'شركة الجوهرة للمجوهرات',
        email: '<EMAIL>',
        phoneNumber: '0503333333',
        address: 'الدمام، حي الشاطئ، شارع الكورنيش',
        city: 'الدمام',
        country: 'السعودية',
        businessType: 'متجر مجوهرات',
        isActive: true,
        defaultDeliveryFee: 25,
        defaultCurrency: 'SAR',
        subscriptionPlan: 'enterprise' as const,
        notificationSettings: {
          orderCreated: true,
          orderStatusChanged: true,
          orderDelivered: true,
          orderReturned: true,
          dailyReport: true,
          weeklyReport: true,
          monthlyReport: true,
        }
      }
    ];

    const companyIds: string[] = [];
    for (const company of companies) {
      const companyId = await CompanyService.createCompany(company);
      companyIds.push(companyId);
    }

    return companyIds;
  }

  // Seed demo users
  static async seedUsers(companyIds: string[]): Promise<string[]> {
    const users = [
      {
        name: 'أحمد محمد',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin' as const,
        isActive: true,
        phoneNumber: '0501234567',
        permissions: defaultPermissions.admin,
      },
      {
        name: 'فاطمة علي',
        username: 'manager',
        email: '<EMAIL>',
        role: 'manager' as const,
        isActive: true,
        phoneNumber: '0507654321',
        permissions: defaultPermissions.manager,
      },
      {
        name: 'محمد سالم',
        username: 'employee',
        email: '<EMAIL>',
        role: 'employee' as const,
        isActive: true,
        phoneNumber: '0509876543',
        permissions: defaultPermissions.employee,
      },
      {
        name: 'خالد أحمد',
        username: 'rep1',
        email: '<EMAIL>',
        role: 'مندوب' as const,
        isActive: true,
        phoneNumber: '0501111111',
        permissions: defaultPermissions.مندوب,
      },
      {
        name: 'سارة محمد',
        username: 'rep2',
        email: '<EMAIL>',
        role: 'مندوب' as const,
        isActive: true,
        phoneNumber: '0502222222',
        permissions: defaultPermissions.مندوب,
      },
      {
        name: 'عبدالله علي',
        username: 'rep3',
        email: '<EMAIL>',
        role: 'مندوب' as const,
        isActive: true,
        phoneNumber: '0503333333',
        permissions: defaultPermissions.مندوب,
      }
    ];

    const userIds: string[] = [];
    for (const user of users) {
      const userId = await UserService.createUser(user);
      userIds.push(userId);
      
      // Add users to companies
      if (companyIds.length > 0) {
        const companyIndex = userIds.length % companyIds.length;
        await CompanyService.addUserToCompany(companyIds[companyIndex], userId);
      }
    }

    return userIds;
  }

  // Seed demo orders
  static async seedOrders(companyIds: string[], userIds: string[]): Promise<string[]> {
    if (companyIds.length === 0 || userIds.length === 0) {
      console.warn('No companies or users found, skipping order seeding');
      return [];
    }

    const orders = [
      {
        orderNumber: 'ORD-001',
        companyId: companyIds[0],
        companyName: 'شركة المسرة',
        clientPhoneNumber: '0501111111',
        customerName: 'سارة أحمد',
        mobileNumber: '0501234567',
        address: 'الرياض، حي النخيل، شارع الملك فهد',
        city: 'الرياض',
        items: [
          { id: '1', name: 'فستان صيفي', quantity: 1, price: 150, total: 150 },
          { id: '2', name: 'حقيبة يد', quantity: 1, price: 80, total: 80 },
        ],
        amount: 230,
        currency: 'SAR',
        paymentMethod: 'cash' as const,
        status: 'pending' as const,
        agentId: userIds[3], // First representative
        agentName: 'خالد أحمد',
        deliveryAttempts: 0,
        maxDeliveryAttempts: 3,
        deliveryFee: 15,
        priority: 'normal' as const,
        notes: 'يرجى التوصيل بعد الساعة 2 ظهراً',
      },
      {
        orderNumber: 'ORD-002',
        companyId: companyIds[1],
        companyName: 'شركة البرق',
        clientPhoneNumber: '0502222222',
        customerName: 'محمد علي',
        mobileNumber: '0507654321',
        address: 'جدة، حي الصفا، شارع التحلية',
        city: 'جدة',
        items: [
          { id: '3', name: 'ساعة ذكية', quantity: 1, price: 500, total: 500 },
        ],
        amount: 500,
        updatedAmount: 480,
        currency: 'SAR',
        paymentMethod: 'card' as const,
        status: 'confirmed' as const,
        agentId: userIds[4], // Second representative
        agentName: 'سارة محمد',
        deliveryAttempts: 0,
        maxDeliveryAttempts: 3,
        deliveryFee: 20,
        priority: 'high' as const,
      },
      {
        orderNumber: 'ORD-003',
        companyId: companyIds[2],
        companyName: 'شركة الجوهرة',
        clientPhoneNumber: '0503333333',
        customerName: 'نورا سالم',
        mobileNumber: '0509876543',
        address: 'الدمام، حي الشاطئ، شارع الكورنيش',
        city: 'الدمام',
        items: [
          { id: '4', name: 'عقد ذهبي', quantity: 1, price: 1200, total: 1200 },
        ],
        amount: 1200,
        currency: 'SAR',
        paymentMethod: 'transfer' as const,
        status: 'dispatched' as const,
        agentId: userIds[5], // Third representative
        agentName: 'عبدالله علي',
        deliveryAttempts: 1,
        maxDeliveryAttempts: 3,
        deliveryFee: 25,
        priority: 'urgent' as const,
        scheduledDeliveryDate: Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Tomorrow
      }
    ];

    const orderIds: string[] = [];
    for (const order of orders) {
      const orderId = await OrderService.createOrder(order);
      orderIds.push(orderId);
    }

    return orderIds;
  }

  // Check if data already exists
  static async checkDataExists(): Promise<boolean> {
    try {
      const users = await UserService.getUsers();
      const companies = await CompanyService.getCompanies();
      
      return users.length > 0 || companies.length > 0;
    } catch (error) {
      console.error('Error checking data existence:', error);
      return false;
    }
  }

  // Seed data only if it doesn't exist
  static async seedIfEmpty(): Promise<void> {
    try {
      const dataExists = await this.checkDataExists();
      
      if (!dataExists) {
        console.log('No existing data found, seeding demo data...');
        await this.seedAllData();
      } else {
        console.log('Data already exists, skipping seeding');
      }
    } catch (error) {
      console.error('Error in seedIfEmpty:', error);
      throw error;
    }
  }
}
