"use client";

import React, { useState, useRef } from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Home, 
  Image, 
  Upload, 
  Share2, 
  Download,
  Camera,
  FileImage,
  Smartphone,
  MessageSquare,
  Mail,
  Copy,
  Check
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { useToast } from "@/hooks/use-toast";

export default function ImageSharePage() {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>("");
  const [shareText, setShareText] = useState("");
  const [shareMethod, setShareMethod] = useState<string>("whatsapp");
  const [isSharing, setIsSharing] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "نوع ملف غير صحيح",
        description: "يرجى اختيار ملف صورة صالح"
      });
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "حجم الملف كبير",
        description: "يرجى اختيار صورة أصغر من 5 ميجابايت"
      });
      return;
    }

    setSelectedImage(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    toast({
      title: "تم اختيار الصورة",
      description: `تم اختيار: ${file.name}`
    });
  };

  const handleShare = async () => {
    if (!selectedImage) {
      toast({
        variant: "destructive",
        title: "لا توجد صورة",
        description: "يرجى اختيار صورة أولاً"
      });
      return;
    }

    setIsSharing(true);

    try {
      // Simulate upload and sharing process
      await new Promise(resolve => setTimeout(resolve, 2000));

      const shareUrl = `https://rihla-delivery.com/shared/${Date.now()}`;
      const fullText = shareText ? `${shareText}\n\n${shareUrl}` : shareUrl;

      switch (shareMethod) {
        case "whatsapp":
          const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(fullText)}`;
          window.open(whatsappUrl, '_blank');
          break;
          
        case "telegram":
          const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;
          window.open(telegramUrl, '_blank');
          break;
          
        case "email":
          const emailUrl = `mailto:?subject=${encodeURIComponent('صورة مشتركة من رحلة')}&body=${encodeURIComponent(fullText)}`;
          window.open(emailUrl, '_blank');
          break;
          
        case "copy":
          await navigator.clipboard.writeText(fullText);
          setCopiedLink(true);
          setTimeout(() => setCopiedLink(false), 3000);
          break;
      }

      toast({
        title: "تم المشاركة بنجاح",
        description: `تم مشاركة الصورة عبر ${getShareMethodName(shareMethod)}`
      });

    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ في المشاركة",
        description: "فشل في مشاركة الصورة"
      });
    } finally {
      setIsSharing(false);
    }
  };

  const getShareMethodName = (method: string) => {
    switch (method) {
      case "whatsapp": return "واتساب";
      case "telegram": return "تيليجرام";
      case "email": return "البريد الإلكتروني";
      case "copy": return "نسخ الرابط";
      default: return method;
    }
  };

  const getShareMethodIcon = (method: string) => {
    switch (method) {
      case "whatsapp": return <MessageSquare className="h-4 w-4" />;
      case "telegram": return <Smartphone className="h-4 w-4" />;
      case "email": return <Mail className="h-4 w-4" />;
      case "copy": return copiedLink ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />;
      default: return <Share2 className="h-4 w-4" />;
    }
  };

  const clearImage = () => {
    setSelectedImage(null);
    setImagePreview("");
    setShareText("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const downloadImage = () => {
    if (!selectedImage || !imagePreview) return;
    
    const link = document.createElement('a');
    link.href = imagePreview;
    link.download = selectedImage.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <PageHeader
        title="مشاركة الصور"
        description="اختر صورة وشاركها عبر التطبيقات المختلفة"
        actions={
          <Button asChild variant="outline">
            <Link href="/">
              <Home className="h-4 w-4" />
              الرئيسية
            </Link>
          </Button>
        }
      />

      <div className="space-y-6">
        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              اختيار الصورة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="image-upload">اختر صورة للمشاركة</Label>
              <Input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageSelect}
                ref={fileInputRef}
                className="cursor-pointer"
              />
              <p className="text-sm text-muted-foreground">
                الحد الأقصى: 5 ميجابايت • الأنواع المدعومة: JPG, PNG, GIF, WebP
              </p>
            </div>

            {selectedImage && (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="flex items-center gap-1">
                  <FileImage className="h-3 w-3" />
                  {selectedImage.name}
                </Badge>
                <Badge variant="secondary">
                  {(selectedImage.size / 1024 / 1024).toFixed(2)} MB
                </Badge>
                <Button variant="outline" size="sm" onClick={clearImage}>
                  إزالة
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Preview Section */}
        {imagePreview && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                معاينة الصورة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative max-w-md mx-auto">
                  <img
                    src={imagePreview}
                    alt="معاينة الصورة"
                    className="w-full h-auto rounded-lg border shadow-sm"
                    style={{ maxHeight: '400px', objectFit: 'contain' }}
                  />
                </div>
                
                <div className="flex justify-center gap-2">
                  <Button variant="outline" size="sm" onClick={downloadImage}>
                    <Download className="h-4 w-4 mr-2" />
                    تحميل
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => fileInputRef.current?.click()}>
                    <Camera className="h-4 w-4 mr-2" />
                    تغيير الصورة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Share Settings */}
        {selectedImage && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Share2 className="h-5 w-5" />
                إعدادات المشاركة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="share-text">نص المشاركة (اختياري)</Label>
                <Textarea
                  id="share-text"
                  value={shareText}
                  onChange={(e) => setShareText(e.target.value)}
                  placeholder="اكتب نصاً لمرافقة الصورة..."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="share-method">طريقة المشاركة</Label>
                <Select value={shareMethod} onValueChange={setShareMethod}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="whatsapp">واتساب</SelectItem>
                    <SelectItem value="telegram">تيليجرام</SelectItem>
                    <SelectItem value="email">البريد الإلكتروني</SelectItem>
                    <SelectItem value="copy">نسخ الرابط</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button 
                onClick={handleShare}
                disabled={isSharing}
                className="w-full"
                size="lg"
              >
                {getShareMethodIcon(shareMethod)}
                {isSharing ? 'جار المشاركة...' : `مشاركة عبر ${getShareMethodName(shareMethod)}`}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>تعليمات الاستخدام</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• اختر صورة من جهازك (الحد الأقصى 5 ميجابايت)</p>
              <p>• أضف نصاً اختيارياً لمرافقة الصورة</p>
              <p>• اختر طريقة المشاركة المفضلة</p>
              <p>• انقر على "مشاركة" لفتح التطبيق المحدد</p>
              <p>• يمكنك نسخ الرابط ومشاركته يدوياً</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
