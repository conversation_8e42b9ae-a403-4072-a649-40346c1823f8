import { 
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseAuthUser,
  createUserWithEmailAndPassword,
  updateProfile,
  sendPasswordResetEmail,
  updatePassword
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { UserService } from './user-service';
import { FirebaseUser } from '@/lib/models/user';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  username: string;
  role: FirebaseUser['role'];
  phoneNumber?: string;
}

export class AuthService {
  // Sign in with email and password
  static async signIn(credentials: LoginCredentials): Promise<FirebaseUser | null> {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth, 
        credentials.email, 
        credentials.password
      );
      
      const firebaseUser = userCredential.user;
      
      // Get user data from Firestore
      const userData = await UserService.getUserByEmail(firebaseUser.email!);
      
      if (userData && userData.isActive) {
        // Update last login
        await UserService.updateLastLogin(userData.id);
        return userData;
      } else {
        // User not found in Firestore or inactive
        await this.signOut();
        throw new Error('User not found or inactive');
      }
    } catch (error: any) {
      console.error('Error signing in:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Sign out
  static async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  // Register new user
  static async register(userData: RegisterData): Promise<FirebaseUser> {
    try {
      // Check if email already exists in Firestore
      const existingUser = await UserService.getUserByEmail(userData.email);
      if (existingUser) {
        throw new Error('Email already exists');
      }

      // Check if username already exists
      const usernameExists = await UserService.usernameExists(userData.username);
      if (usernameExists) {
        throw new Error('Username already exists');
      }

      // Create Firebase Auth user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        userData.email,
        userData.password
      );

      // Update display name
      await updateProfile(userCredential.user, {
        displayName: userData.name
      });

      // Create user document in Firestore
      const userId = await UserService.createUser({
        name: userData.name,
        username: userData.username,
        email: userData.email,
        role: userData.role,
        isActive: true,
        phoneNumber: userData.phoneNumber,
        permissions: undefined // Will be set by UserService based on role
      });

      // Get the created user
      const newUser = await UserService.getUser(userId);
      if (!newUser) {
        throw new Error('Failed to create user');
      }

      return newUser;
    } catch (error: any) {
      console.error('Error registering user:', error);
      throw new Error(this.getErrorMessage(error.code) || error.message);
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<FirebaseUser | null> {
    try {
      const firebaseUser = auth.currentUser;
      if (firebaseUser && firebaseUser.email) {
        const userData = await UserService.getUserByEmail(firebaseUser.email);
        return userData && userData.isActive ? userData : null;
      }
      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Listen to auth state changes
  static onAuthStateChanged(callback: (user: FirebaseUser | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser && firebaseUser.email) {
        try {
          const userData = await UserService.getUserByEmail(firebaseUser.email);
          callback(userData && userData.isActive ? userData : null);
        } catch (error) {
          console.error('Error in auth state change:', error);
          callback(null);
        }
      } else {
        callback(null);
      }
    });
  }

  // Send password reset email
  static async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      console.error('Error sending password reset email:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Update password
  static async updatePassword(newPassword: string): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }
      await updatePassword(user, newPassword);
    } catch (error: any) {
      console.error('Error updating password:', error);
      throw new Error(this.getErrorMessage(error.code));
    }
  }

  // Update user profile
  static async updateUserProfile(updates: {
    displayName?: string;
    photoURL?: string;
  }): Promise<void> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }
      await updateProfile(user, updates);
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    return auth.currentUser !== null;
  }

  // Get Firebase Auth user
  static getFirebaseUser(): FirebaseAuthUser | null {
    return auth.currentUser;
  }

  // Convert Firebase Auth error codes to user-friendly messages
  private static getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'لم يتم العثور على المستخدم';
      case 'auth/wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'auth/email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'auth/weak-password':
        return 'كلمة المرور ضعيفة';
      case 'auth/invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'auth/user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'auth/too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح';
      case 'auth/network-request-failed':
        return 'خطأ في الاتصال بالشبكة';
      case 'auth/requires-recent-login':
        return 'يتطلب تسجيل دخول حديث';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }
}
