export type OrderStatus = 'pending' | 'confirmed' | 'dispatched' | 'delivered' | 'returned' | 'cancelled';

export interface OrderStatusDetail {
  key: OrderStatus;
  label: string;
  color?: string;
  icon?: string;
}

export interface Company {
  id: string;
  name: string;
  value: string;
  label: string;
}

export interface DateRange {
  from?: Date;
  to?: Date;
}

export interface User {
  id: string;
  name?: string;
  username: string;
  email: string;
  role: 'admin' | 'manager' | 'employee' | 'مندوب';
  permissions?: any;
  isActive?: boolean;
  createdAt: Date;
  lastLogin?: Date;
  phoneNumber?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  companyName: string;
  clientPhoneNumber?: string;
  customerName?: string;
  mobileNumber: string;
  address?: string;
  amount: number;
  updatedAmount?: number;
  status: OrderStatus;
  agentId?: string;
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
  rejectedReason?: string;
  postponeReason?: string;
  partiallyReturnedItems?: number;
  importedClientIdentifier?: string;
}
