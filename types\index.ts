// Order Status Types
export type OrderStatus =
  | 'في الانتظار'
  | 'مؤكد'
  | 'قيد التوصيل'
  | 'تم التسليم'
  | 'راجع'
  | 'ملغي'
  | 'مؤجل'
  | 'تسليم جزئي'
  | 'تغيير سعر'
  | 'إعادة إرسال'
  | 'راجع عند المندوب'
  | 'في انتظار الإسناد';

export interface OrderStatusDetail {
  key: OrderStatus;
  label: string;
  color?: string;
  icon?: string;
}

// User Types
export type UserRole =
  | 'مدير عام'
  | 'مشرف'
  | 'موظف'
  | 'مندوب'
  | 'عميل'
  | 'متابع'
  | 'مدير مركز'
  | 'مدير فرع'
  | 'محاسب'
  | 'مرسل';

export interface UserPermissions {
  orders?: {
    view?: boolean;
    create?: boolean;
    edit?: boolean;
    delete?: boolean;
  };
  users?: {
    view?: boolean;
    create?: boolean;
    edit?: boolean;
    delete?: boolean;
  };
  accounting?: {
    view?: boolean;
    edit?: boolean;
  };
  archive?: {
    view?: boolean;
  };
  reports?: {
    view?: boolean;
  };
}

export interface User {
  id: string;
  name?: string;
  username: string;
  email: string;
  role: UserRole;
  permissions?: UserPermissions;
  isActive: boolean;
  governorate?: string;
  district?: string;
  centerId?: string;
  branchId?: string;
  createdAt: string;
  updatedAt: string;
  lastLogin?: string;
  phoneNumber?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  companyName: string;
  clientPhoneNumber?: string;
  customerName?: string;
  mobileNumber: string;
  address?: string;
  amount: number;
  updatedAmount?: number;
  status: OrderStatus;
  agentId?: string;
  centerId?: string;
  branchId?: string;
  creatorId?: string;
  lastUpdatedById?: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  rejectedReason?: string;
  isArchived?: boolean;
  statusBeforeArchive?: OrderStatus;
  isAccountedForRep?: boolean;
  postponeReason?: string;
  partiallyReturnedItems?: number;
  importedClientIdentifier?: string;
}

// Accounting Types
export interface AccountingRecordRep {
  settlementId: string;
  representativeId: string;
  representativeName: string;
  date: string;
  totalCollectedAmount: number;
  totalCommission: number;
  netPayableToRep: number;
  paymentStatus: 'pending' | 'paid';
  orders: {
    id: string;
    customerName: string;
    amount: number;
    updatedAmount?: number;
    commission: number;
  }[];
  creatorId: string;
}

export interface AccountingRecordClient {
  settlementId: string;
  clientId: string;
  clientName: string;
  date: string;
  totalDeliveredAmount: number;
  netPayableToClient: number;
  paymentStatus: 'pending' | 'paid';
  orders: {
    id: string;
    customerName: string;
    amount: number;
    updatedAmount?: number;
  }[];
  creatorId: string;
}

// Company Types
export interface Company {
  id: string;
  name: string;
  value: string;
  label: string;
}

// Date Range
export interface DateRange {
  from?: Date;
  to?: Date;
}
