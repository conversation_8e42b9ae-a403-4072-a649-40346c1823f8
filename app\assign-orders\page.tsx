"use client";

import React, { useState, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Home, UserCheck, Package, Search, Filter } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import { useToast } from "@/hooks/use-toast";
import type { Order, User } from "@/types";
import { getOrders } from "@/lib/order-data";
import { getUsers } from "@/lib/user-data";

export default function AssignOrdersPage() {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [agents, setAgents] = useState<User[]>([]);
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [selectedAgent, setSelectedAgent] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch unassigned orders
      const ordersData = await getOrders({ 
        isArchived: false,
        agentId: null // Only unassigned orders
      });
      setOrders(ordersData);

      // Fetch available agents
      const agentsData = await getUsers({ role: 'مندوب', isActive: true });
      setAgents(agentsData);
      
    } catch (error) {
      console.error("Error fetching data:", error);
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "فشل تحميل البيانات"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = searchTerm === "" || 
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.mobileNumber.includes(searchTerm);
    
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleSelectOrder = (orderId: string) => {
    setSelectedOrders(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(orderId)) {
        newSelected.delete(orderId);
      } else {
        newSelected.add(orderId);
      }
      return newSelected;
    });
  };

  const handleSelectAll = () => {
    if (selectedOrders.size === filteredOrders.length) {
      setSelectedOrders(new Set());
    } else {
      setSelectedOrders(new Set(filteredOrders.map(order => order.id)));
    }
  };

  const handleAssignOrders = async () => {
    if (selectedOrders.size === 0) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يرجى تحديد طلب واحد على الأقل"
      });
      return;
    }

    if (!selectedAgent) {
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "يرجى اختيار مندوب"
      });
      return;
    }

    try {
      // Here you would call an API to assign orders
      // For now, we'll just show a success message
      
      toast({
        title: "تم الإسناد بنجاح",
        description: `تم إسناد ${selectedOrders.size} طلب للمندوب المحدد`
      });
      
      setSelectedOrders(new Set());
      setSelectedAgent("");
      fetchData(); // Refresh data
      
    } catch (error) {
      console.error("Error assigning orders:", error);
      toast({
        variant: "destructive",
        title: "خطأ",
        description: "فشل إسناد الطلبات"
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-IQ', {
      style: 'currency',
      currency: 'IQD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (!currentUser?.permissions?.orders?.edit) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="text-center py-10">
            <p className="text-lg text-muted-foreground">
              ليس لديك صلاحية لإسناد الطلبات
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <PageHeader
        title="إسناد الطلبات للمندوبين"
        description="إسناد الطلبات غير المسندة للمندوبين المتاحين"
        actions={
          <Button asChild variant="outline">
            <Link href="/">
              <Home className="h-4 w-4" />
              الرئيسية
            </Link>
          </Button>
        }
      />

      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              البحث والفلترة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="بحث بالوصل، الشركة، الهاتف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="في الانتظار">في الانتظار</SelectItem>
                  <SelectItem value="مؤكد">مؤكد</SelectItem>
                  <SelectItem value="في انتظار الإسناد">في انتظار الإسناد</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  المحدد: {selectedOrders.size} من {filteredOrders.length}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Assignment Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="h-5 w-5" />
              إسناد للمندوب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 items-end">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">
                  اختيار المندوب
                </label>
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر مندوب..." />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map(agent => (
                      <SelectItem key={agent.id} value={agent.id}>
                        {agent.name} - {agent.governorate}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <Button 
                onClick={handleAssignOrders}
                disabled={selectedOrders.size === 0 || !selectedAgent}
                className="w-full sm:w-auto"
              >
                <UserCheck className="h-4 w-4 mr-2" />
                إسناد الطلبات المحددة ({selectedOrders.size})
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                الطلبات غير المسندة ({filteredOrders.length})
              </CardTitle>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={filteredOrders.length === 0}
              >
                <Checkbox
                  checked={selectedOrders.size === filteredOrders.length && filteredOrders.length > 0}
                  className="ml-2"
                />
                {selectedOrders.size === filteredOrders.length && filteredOrders.length > 0 
                  ? 'إلغاء تحديد الكل' 
                  : 'تحديد الكل'
                }
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-10">
                <p>جار التحميل...</p>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-10 text-muted-foreground">
                <Package className="mx-auto h-12 w-12 mb-4" />
                <p className="text-lg font-semibold">لا توجد طلبات غير مسندة</p>
                <p>جميع الطلبات مسندة للمندوبين</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredOrders.map((order) => (
                  <Card 
                    key={order.id}
                    className={`cursor-pointer transition-all duration-200 ${
                      selectedOrders.has(order.id) 
                        ? 'ring-2 ring-primary ring-offset-2' 
                        : 'hover:shadow-md'
                    }`}
                    onClick={() => handleSelectOrder(order.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="font-semibold">{order.orderNumber}</div>
                        <Checkbox
                          checked={selectedOrders.has(order.id)}
                          onChange={() => handleSelectOrder(order.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2 text-sm">
                        <p><strong>الشركة:</strong> {order.companyName}</p>
                        <p><strong>الهاتف:</strong> {order.mobileNumber}</p>
                        <p><strong>المبلغ:</strong> {formatCurrency(order.amount)}</p>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline">{order.status}</Badge>
                          <span className="text-xs text-muted-foreground">
                            {new Date(order.createdAt).toLocaleDateString('ar-SA')}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
