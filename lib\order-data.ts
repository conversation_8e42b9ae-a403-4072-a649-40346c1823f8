export type OrderStatus = 'pending' | 'confirmed' | 'dispatched' | 'delivered' | 'returned' | 'cancelled';

export interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  createdAt: Date;
  updatedAt: Date;
  assignedTo?: string;
  deliveryDate?: Date;
  notes?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

export const ORDER_STATUSES_DETAILS = [
  { key: 'pending' as OrderStatus, label: 'في الانتظار', color: 'yellow' },
  { key: 'confirmed' as OrderStatus, label: 'مؤكد', color: 'blue' },
  { key: 'dispatched' as OrderStatus, label: 'تم الإرسال', color: 'purple' },
  { key: 'delivered' as OrderStatus, label: 'تم التسليم', color: 'green' },
  { key: 'returned' as OrderStatus, label: 'مرتجع', color: 'orange' },
  { key: 'cancelled' as OrderStatus, label: 'ملغي', color: 'red' },
];

// Mock data for demo purposes
const mockOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    customerName: 'سارة أحمد',
    customerPhone: '0501234567',
    customerAddress: 'الرياض، حي النخيل، شارع الملك فهد',
    items: [
      { id: '1', name: 'فستان صيفي', quantity: 1, price: 150, total: 150 },
      { id: '2', name: 'حقيبة يد', quantity: 1, price: 80, total: 80 },
    ],
    totalAmount: 230,
    status: 'pending',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    notes: 'يرجى التوصيل بعد الساعة 2 ظهراً',
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'محمد علي',
    customerPhone: '0507654321',
    customerAddress: 'جدة، حي الصفا، شارع التحلية',
    items: [
      { id: '3', name: 'ساعة ذكية', quantity: 1, price: 500, total: 500 },
    ],
    totalAmount: 500,
    status: 'confirmed',
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-15'),
    assignedTo: 'أحمد المندوب',
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    customerName: 'نورا سالم',
    customerPhone: '0509876543',
    customerAddress: 'الدمام، حي الشاطئ، شارع الكورنيش',
    items: [
      { id: '4', name: 'كتاب طبخ', quantity: 2, price: 45, total: 90 },
      { id: '5', name: 'أدوات مطبخ', quantity: 1, price: 120, total: 120 },
    ],
    totalAmount: 210,
    status: 'dispatched',
    createdAt: new Date('2024-01-13'),
    updatedAt: new Date('2024-01-15'),
    assignedTo: 'فهد المندوب',
    deliveryDate: new Date('2024-01-16'),
  },
];

export async function getOrders(): Promise<Order[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockOrders;
}

export async function getOrder(searchTerm: string): Promise<Order | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const order = mockOrders.find(
    o => o.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
         o.customerPhone.includes(searchTerm) ||
         o.id === searchTerm
  );
  
  return order || null;
}

export async function createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const newOrder: Order = {
    ...orderData,
    id: Math.random().toString(36).substr(2, 9),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  mockOrders.push(newOrder);
  return newOrder;
}

export async function updateOrder(id: string, updates: Partial<Order>): Promise<Order | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const orderIndex = mockOrders.findIndex(o => o.id === id);
  if (orderIndex === -1) return null;
  
  mockOrders[orderIndex] = {
    ...mockOrders[orderIndex],
    ...updates,
    updatedAt: new Date(),
  };
  
  return mockOrders[orderIndex];
}

export function generateOrderNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 3);
  return `ORD-${timestamp}-${random}`.toUpperCase();
}
