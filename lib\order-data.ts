import type { Order, OrderStatus, AccountingRecordRep } from "@/types";
import { MockOrderData } from "./mock-orders";

export const ORDER_STATUSES_DETAILS = [
  { key: 'في الانتظار' as OrderStatus, label: 'في الانتظار', color: 'yellow' },
  { key: 'مؤكد' as OrderStatus, label: 'مؤكد', color: 'blue' },
  { key: 'في الطريق' as OrderStatus, label: 'في الطريق', color: 'purple' },
  { key: 'تم التسليم' as OrderStatus, label: 'تم التسليم', color: 'green' },
  { key: 'تسليم جزئي' as OrderStatus, label: 'تسليم جزئي', color: 'orange' },
  { key: 'مرتجع' as OrderStatus, label: 'مرتجع', color: 'orange' },
  { key: 'ملغي' as OrderStatus, label: 'ملغي', color: 'red' },
  { key: 'مؤرشف' as OrderStatus, label: 'مؤرشف', color: 'gray' },
];

export function getOrderStatusDetail(status: OrderStatus) {
  return ORDER_STATUSES_DETAILS.find(s => s.key === status);
}

// Export functions that use MockOrderData
export async function getOrders(filters?: {
  status?: OrderStatus | OrderStatus[];
  agentId?: string;
  isArchived?: boolean;
  isAccountedForRep?: boolean;
  companyName?: string;
  orderNumber?: string;
}): Promise<Order[]> {
  return MockOrderData.getOrders(filters);
}

export async function getOrder(id: string): Promise<Order | null> {
  return MockOrderData.getOrder(id);
}

export async function updateOrder(id: string, updates: Partial<Order>): Promise<Order | null> {
  return MockOrderData.updateOrder(id, updates);
}

export async function addRepSettlement(settlement: Omit<AccountingRecordRep, 'settlementId'>): Promise<AccountingRecordRep> {
  return MockOrderData.addRepSettlement(settlement);
}

export async function getRepSettlement(settlementId: string): Promise<AccountingRecordRep | null> {
  return MockOrderData.getRepSettlement(settlementId);
}

export async function getRepSettlements(filters?: {
  representativeId?: string;
  paymentStatus?: string;
}): Promise<AccountingRecordRep[]> {
  return MockOrderData.getRepSettlements(filters);
}
// Additional utility functions
export function generateOrderNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 3);
  return `R-${timestamp}-${random}`.toUpperCase();
}

export async function createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
  // This would typically create a new order via MockOrderData
  // For now, just simulate the API call
  await new Promise(resolve => setTimeout(resolve, 800));

  const newOrder: Order = {
    ...orderData,
    id: `order-${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  return newOrder;
}


