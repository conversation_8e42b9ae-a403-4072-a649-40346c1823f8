export type OrderStatus = 'pending' | 'confirmed' | 'dispatched' | 'delivered' | 'returned' | 'cancelled';

export interface Order {
  id: string;
  orderNumber: string;
  companyName: string;
  clientPhoneNumber?: string;
  customerName?: string;
  mobileNumber: string;
  address?: string;
  amount: number;
  updatedAmount?: number;
  status: OrderStatus;
  agentId?: string;
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
  rejectedReason?: string;
  postponeReason?: string;
  partiallyReturnedItems?: number;
  importedClientIdentifier?: string;
  items?: OrderItem[];
  totalAmount?: number;
  customerPhone?: string;
  customerAddress?: string;
  assignedTo?: string;
  deliveryDate?: Date;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  total: number;
}

export const ORDER_STATUSES_DETAILS = [
  { key: 'pending' as OrderStatus, label: 'في الانتظار', color: 'yellow' },
  { key: 'confirmed' as OrderStatus, label: 'مؤكد', color: 'blue' },
  { key: 'dispatched' as OrderStatus, label: 'تم الإرسال', color: 'purple' },
  { key: 'delivered' as OrderStatus, label: 'تم التسليم', color: 'green' },
  { key: 'returned' as OrderStatus, label: 'مرتجع', color: 'orange' },
  { key: 'cancelled' as OrderStatus, label: 'ملغي', color: 'red' },
];

// Mock data for demo purposes
const mockOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    companyName: 'شركة المسرة',
    clientPhoneNumber: '0501111111',
    customerName: 'سارة أحمد',
    mobileNumber: '0501234567',
    address: 'الرياض، حي النخيل، شارع الملك فهد',
    amount: 230,
    status: 'pending',
    agentId: '4',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    notes: 'يرجى التوصيل بعد الساعة 2 ظهراً',
    items: [
      { id: '1', name: 'فستان صيفي', quantity: 1, price: 150, total: 150 },
      { id: '2', name: 'حقيبة يد', quantity: 1, price: 80, total: 80 },
    ],
    totalAmount: 230,
    customerPhone: '0501234567',
    customerAddress: 'الرياض، حي النخيل، شارع الملك فهد',
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    companyName: 'شركة البرق',
    clientPhoneNumber: '0502222222',
    customerName: 'محمد علي',
    mobileNumber: '0507654321',
    address: 'جدة، حي الصفا، شارع التحلية',
    amount: 500,
    updatedAmount: 480,
    status: 'confirmed',
    agentId: '5',
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-15'),
    items: [
      { id: '3', name: 'ساعة ذكية', quantity: 1, price: 500, total: 500 },
    ],
    totalAmount: 500,
    customerPhone: '0507654321',
    customerAddress: 'جدة، حي الصفا، شارع التحلية',
    assignedTo: 'سارة محمد',
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    companyName: 'شركة الجوهرة',
    clientPhoneNumber: '0503333333',
    customerName: 'نورا سالم',
    mobileNumber: '0509876543',
    address: 'الدمام، حي الشاطئ، شارع الكورنيش',
    amount: 210,
    status: 'dispatched',
    agentId: '6',
    createdAt: new Date('2024-01-13'),
    updatedAt: new Date('2024-01-15'),
    deliveryDate: new Date('2024-01-16'),
    items: [
      { id: '4', name: 'كتاب طبخ', quantity: 2, price: 45, total: 90 },
      { id: '5', name: 'أدوات مطبخ', quantity: 1, price: 120, total: 120 },
    ],
    totalAmount: 210,
    customerPhone: '0509876543',
    customerAddress: 'الدمام، حي الشاطئ، شارع الكورنيش',
    assignedTo: 'عبدالله علي',
  },
  {
    id: '4',
    orderNumber: 'ORD-004',
    companyName: 'شركة الساعي',
    clientPhoneNumber: '0504444444',
    customerName: 'أحمد خالد',
    mobileNumber: '0501111222',
    address: 'مكة، حي العزيزية، شارع الحرم',
    amount: 350,
    status: 'delivered',
    agentId: '4',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-16'),
    notes: 'تم التسليم بنجاح',
  },
  {
    id: '5',
    orderNumber: 'ORD-005',
    companyName: 'شركة المسرة',
    clientPhoneNumber: '0501111111',
    customerName: 'فاطمة محمد',
    mobileNumber: '0505555555',
    address: 'الطائف، حي الشهداء، شارع الملك عبدالعزيز',
    amount: 180,
    status: 'returned',
    agentId: '5',
    createdAt: new Date('2024-01-11'),
    updatedAt: new Date('2024-01-17'),
    rejectedReason: 'العميل غير متواجد',
  },
  {
    id: '6',
    orderNumber: 'ORD-006',
    companyName: 'شركة البرق',
    clientPhoneNumber: '0502222222',
    customerName: 'عبدالرحمن سعد',
    mobileNumber: '0506666666',
    address: 'الخبر، حي الراكة، شارع الأمير فيصل',
    amount: 420,
    status: 'cancelled',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
    notes: 'ألغي بناءً على طلب العميل',
  },
];

export async function getOrders(): Promise<Order[]> {
  try {
    // Try to get from Firebase first
    const { OrderService } = await import('@/lib/services/order-service');
    const result = await OrderService.getOrders();

    // Convert Firebase orders to legacy format
    return result.orders.map(order => ({
      ...order,
      customerPhone: order.mobileNumber,
      customerAddress: order.address,
      totalAmount: order.amount,
      assignedTo: order.agentName,
    }));
  } catch (error) {
    console.warn('Firebase not available, using mock data:', error);
    // Fallback to mock data
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockOrders;
  }
}

export async function getOrder(searchTerm: string): Promise<Order | null> {
  try {
    // Try to get from Firebase first
    const { OrderService } = await import('@/lib/services/order-service');
    const orders = await OrderService.searchOrders(searchTerm);

    if (orders.length > 0) {
      const order = orders[0];
      return {
        ...order,
        customerPhone: order.mobileNumber,
        customerAddress: order.address,
        totalAmount: order.amount,
        assignedTo: order.agentName,
      };
    }

    return null;
  } catch (error) {
    console.warn('Firebase not available, using mock data:', error);
    // Fallback to mock data
    await new Promise(resolve => setTimeout(resolve, 300));

    const order = mockOrders.find(
      o => o.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
           o.customerPhone?.includes(searchTerm) ||
           o.id === searchTerm
    );

    return order || null;
  }
}

export async function createOrder(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const newOrder: Order = {
    ...orderData,
    id: Math.random().toString(36).substr(2, 9),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  mockOrders.push(newOrder);
  return newOrder;
}

export async function updateOrder(id: string, updates: Partial<Order>): Promise<Order | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const orderIndex = mockOrders.findIndex(o => o.id === id);
  if (orderIndex === -1) return null;
  
  mockOrders[orderIndex] = {
    ...mockOrders[orderIndex],
    ...updates,
    updatedAt: new Date(),
  };
  
  return mockOrders[orderIndex];
}

export function generateOrderNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 3);
  return `ORD-${timestamp}-${random}`.toUpperCase();
}

export function getOrderStatusDetail(status: OrderStatus) {
  return ORDER_STATUSES_DETAILS.find(detail => detail.key === status);
}
