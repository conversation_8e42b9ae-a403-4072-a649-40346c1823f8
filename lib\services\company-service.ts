import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Company, CompanySettings, CompanyStats } from '@/lib/models/company';

const COMPANIES_COLLECTION = 'companies';
const COMPANY_SETTINGS_COLLECTION = 'companySettings';
const COMPANY_STATS_COLLECTION = 'companyStats';

export class CompanyService {
  // Get all companies
  static async getCompanies(): Promise<Company[]> {
    try {
      const companiesRef = collection(db, COMPANIES_COLLECTION);
      const q = query(companiesRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Company));
    } catch (error) {
      console.error('Error getting companies:', error);
      throw error;
    }
  }

  // Get active companies
  static async getActiveCompanies(): Promise<Company[]> {
    try {
      const companiesRef = collection(db, COMPANIES_COLLECTION);
      const q = query(
        companiesRef, 
        where('isActive', '==', true),
        orderBy('name')
      );
      const snapshot = await getDocs(q);
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Company));
    } catch (error) {
      console.error('Error getting active companies:', error);
      throw error;
    }
  }

  // Get company by ID
  static async getCompany(companyId: string): Promise<Company | null> {
    try {
      const companyRef = doc(db, COMPANIES_COLLECTION, companyId);
      const companySnap = await getDoc(companyRef);
      
      if (companySnap.exists()) {
        return {
          id: companySnap.id,
          ...companySnap.data()
        } as Company;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting company:', error);
      throw error;
    }
  }

  // Create new company
  static async createCompany(companyData: Omit<Company, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const newCompany = {
        ...companyData,
        currentMonthOrders: 0,
        accountBalance: 0,
        userIds: [],
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      const docRef = await addDoc(collection(db, COMPANIES_COLLECTION), newCompany);
      
      // Create default settings
      await this.createDefaultSettings(docRef.id);
      
      // Create initial stats
      await this.createInitialStats(docRef.id);
      
      return docRef.id;
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  }

  // Update company
  static async updateCompany(companyId: string, updates: Partial<Company>): Promise<void> {
    try {
      const companyRef = doc(db, COMPANIES_COLLECTION, companyId);
      await updateDoc(companyRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating company:', error);
      throw error;
    }
  }

  // Add user to company
  static async addUserToCompany(companyId: string, userId: string): Promise<void> {
    try {
      const companyRef = doc(db, COMPANIES_COLLECTION, companyId);
      const company = await this.getCompany(companyId);
      
      if (company && !company.userIds.includes(userId)) {
        await updateDoc(companyRef, {
          userIds: [...company.userIds, userId],
          updatedAt: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Error adding user to company:', error);
      throw error;
    }
  }

  // Remove user from company
  static async removeUserFromCompany(companyId: string, userId: string): Promise<void> {
    try {
      const companyRef = doc(db, COMPANIES_COLLECTION, companyId);
      const company = await this.getCompany(companyId);
      
      if (company) {
        await updateDoc(companyRef, {
          userIds: company.userIds.filter(id => id !== userId),
          updatedAt: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Error removing user from company:', error);
      throw error;
    }
  }

  // Increment monthly orders
  static async incrementMonthlyOrders(companyId: string): Promise<void> {
    try {
      const companyRef = doc(db, COMPANIES_COLLECTION, companyId);
      await updateDoc(companyRef, {
        currentMonthOrders: increment(1),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error incrementing monthly orders:', error);
      throw error;
    }
  }

  // Update account balance
  static async updateAccountBalance(companyId: string, amount: number): Promise<void> {
    try {
      const companyRef = doc(db, COMPANIES_COLLECTION, companyId);
      await updateDoc(companyRef, {
        accountBalance: increment(amount),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating account balance:', error);
      throw error;
    }
  }

  // Get company settings
  static async getCompanySettings(companyId: string): Promise<CompanySettings | null> {
    try {
      const settingsRef = collection(db, COMPANY_SETTINGS_COLLECTION);
      const q = query(settingsRef, where('companyId', '==', companyId), limit(1));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as CompanySettings;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting company settings:', error);
      throw error;
    }
  }

  // Update company settings
  static async updateCompanySettings(companyId: string, settings: Partial<CompanySettings>): Promise<void> {
    try {
      const settingsRef = collection(db, COMPANY_SETTINGS_COLLECTION);
      const q = query(settingsRef, where('companyId', '==', companyId), limit(1));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const docRef = snapshot.docs[0].ref;
        await updateDoc(docRef, {
          ...settings,
          updatedAt: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Error updating company settings:', error);
      throw error;
    }
  }

  // Get company stats
  static async getCompanyStats(companyId: string): Promise<CompanyStats | null> {
    try {
      const statsRef = collection(db, COMPANY_STATS_COLLECTION);
      const q = query(statsRef, where('companyId', '==', companyId), limit(1));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          id: doc.id,
          ...doc.data()
        } as CompanyStats;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting company stats:', error);
      throw error;
    }
  }

  // Update company stats
  static async updateCompanyStats(companyId: string, stats: Partial<CompanyStats>): Promise<void> {
    try {
      const statsRef = collection(db, COMPANY_STATS_COLLECTION);
      const q = query(statsRef, where('companyId', '==', companyId), limit(1));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const docRef = snapshot.docs[0].ref;
        await updateDoc(docRef, {
          ...stats,
          lastUpdated: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Error updating company stats:', error);
      throw error;
    }
  }

  // Create default settings for new company
  private static async createDefaultSettings(companyId: string): Promise<void> {
    try {
      const defaultSettings: Omit<CompanySettings, 'id'> = {
        companyId,
        orderNumberPrefix: 'ORD',
        orderNumberFormat: 'ORD-{timestamp}-{random}',
        autoAssignOrders: false,
        maxDeliveryAttempts: 3,
        defaultOrderPriority: 'normal',
        workingHours: {
          start: '08:00',
          end: '18:00'
        },
        workingDays: [0, 1, 2, 3, 4, 5, 6], // All days
        deliveryZones: [],
        notificationTemplates: {
          orderConfirmation: 'تم تأكيد طلبكم رقم {orderNumber}',
          orderDispatched: 'تم إرسال طلبكم رقم {orderNumber}',
          orderDelivered: 'تم تسليم طلبكم رقم {orderNumber}',
          orderReturned: 'تم إرجاع طلبكم رقم {orderNumber}',
          orderCancelled: 'تم إلغاء طلبكم رقم {orderNumber}'
        },
        apiKeys: {
          public: this.generateApiKey(),
          private: this.generateApiKey()
        },
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await addDoc(collection(db, COMPANY_SETTINGS_COLLECTION), defaultSettings);
    } catch (error) {
      console.error('Error creating default settings:', error);
      throw error;
    }
  }

  // Create initial stats for new company
  private static async createInitialStats(companyId: string): Promise<void> {
    try {
      const initialStats: Omit<CompanyStats, 'id'> = {
        companyId,
        totalOrders: 0,
        pendingOrders: 0,
        confirmedOrders: 0,
        dispatchedOrders: 0,
        deliveredOrders: 0,
        returnedOrders: 0,
        cancelledOrders: 0,
        totalRevenue: 0,
        monthlyRevenue: 0,
        averageOrderValue: 0,
        deliverySuccessRate: 0,
        averageDeliveryTime: 0,
        customerSatisfactionRate: 0,
        todayOrders: 0,
        weekOrders: 0,
        monthOrders: 0,
        lastUpdated: serverTimestamp(),
      };

      await addDoc(collection(db, COMPANY_STATS_COLLECTION), initialStats);
    } catch (error) {
      console.error('Error creating initial stats:', error);
      throw error;
    }
  }

  // Generate API key
  private static generateApiKey(): string {
    return Math.random().toString(36).substr(2) + Math.random().toString(36).substr(2);
  }
}
