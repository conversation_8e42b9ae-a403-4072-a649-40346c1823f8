# دليل إعداد تطبيق الأندرويد - Rihla Delivery

## 📱 متطلبات النظام

### البرامج المطلوبة:
1. **Android Studio** (أحدث إصدار)
2. **Java Development Kit (JDK 8+)**
3. **Android SDK** (API Level 34)
4. **Node.js** (18+)
5. **npm** أو **yarn**

### إعدادات النظام:
- **نظام التشغيل**: Windows 10+, macOS 10.14+, أو Linux
- **الذاكرة**: 8GB RAM (مستحسن 16GB)
- **التخزين**: 10GB مساحة فارغة

## 🚀 التثبيت والإعداد

### 1. تثبيت Android Studio
1. حمل Android Studio من [الموقع الرسمي](https://developer.android.com/studio)
2. ثبت البرنامج واتبع معالج الإعداد
3. تأكد من تثبيت:
   - Android SDK Platform 34
   - Android SDK Build-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM)

### 2. إعداد متغيرات البيئة
```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# macOS/Linux
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### 3. تثبيت مكتبات المشروع
```bash
# في مجلد المشروع الرئيسي
npm install

# تثبيت Capacitor
npm install @capacitor/core @capacitor/cli
npm install @capacitor/android
```

## 📱 تشغيل التطبيق على الأندرويد

### الطريقة الأولى: استخدام الملف التفاعلي
```bash
# Windows
run-app.bat
# اختر الخيار [2] للأندرويد

# Linux/Mac
./run-app.sh
# اختر الخيار [2] للأندرويد
```

### الطريقة الثانية: الأوامر اليدوية
```bash
# 1. بناء التطبيق
npm run build

# 2. مزامنة Capacitor
npx cap sync android

# 3. فتح Android Studio
npx cap open android

# 4. في Android Studio، اضغط على زر "Run"
```

## 🔧 إعداد الجهاز

### للجهاز الحقيقي:
1. فعل **Developer Options** في الجهاز
2. فعل **USB Debugging**
3. وصل الجهاز بالكمبيوتر
4. اقبل تصريح USB Debugging

### للمحاكي:
1. افتح **AVD Manager** في Android Studio
2. أنشئ جهاز افتراضي جديد
3. اختر **Pixel 6** أو أي جهاز حديث
4. اختر **API Level 34** (Android 14)
5. شغل المحاكي

## 📦 إنشاء APK للتوزيع

### الطريقة السريعة:
```bash
# استخدم الملف التفاعلي
run-app.bat  # أو ./run-app.sh
# اختر الخيار [8] لإنشاء APK
```

### الطريقة اليدوية:
1. افتح Android Studio
2. اذهب إلى **Build > Generate Signed Bundle / APK**
3. اختر **APK**
4. أنشئ مفتاح جديد أو استخدم موجود:
   - **Key store path**: مكان حفظ المفتاح
   - **Password**: كلمة مرور المفتاح
   - **Key alias**: اسم المفتاح
   - **Validity**: 25 سنة
5. اختر **release** build
6. انتظر حتى يتم إنشاء APK

### مكان حفظ APK:
```
android/app/build/outputs/apk/release/app-release.apk
```

## 🛠️ إعدادات متقدمة

### تخصيص الأيقونة:
1. ضع الأيقونات في:
   ```
   android/app/src/main/res/mipmap-*/ic_launcher.png
   ```
2. استخدم [Android Asset Studio](https://romannurik.github.io/AndroidAssetStudio/) لإنشاء الأيقونات

### تخصيص شاشة البداية:
1. عدل ملف:
   ```
   android/app/src/main/res/drawable/splash_screen.xml
   ```
2. غير الألوان في:
   ```
   android/app/src/main/res/values/colors.xml
   ```

### إضافة الأذونات:
عدل ملف `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
```

## 🔍 استكشاف الأخطاء

### مشكلة: Android Studio لا يتعرف على المشروع
**الحل:**
```bash
npx cap sync android
```

### مشكلة: Build failed
**الحل:**
1. تأكد من إصدار Java (JDK 8+)
2. نظف المشروع: **Build > Clean Project**
3. أعد بناء المشروع: **Build > Rebuild Project**

### مشكلة: الجهاز غير مرئي
**الحل:**
1. تأكد من تفعيل USB Debugging
2. جرب كابل USB آخر
3. أعد تشغيل ADB:
   ```bash
   adb kill-server
   adb start-server
   ```

### مشكلة: التطبيق يتوقف عند التشغيل
**الحل:**
1. تحقق من Logcat في Android Studio
2. تأكد من بناء التطبيق بنجاح: `npm run build`
3. تأكد من مزامنة Capacitor: `npx cap sync android`

## 📊 تحسين الأداء

### تقليل حجم APK:
1. فعل **minifyEnabled** في `build.gradle`
2. استخدم **ProGuard** أو **R8**
3. احذف الموارد غير المستخدمة

### تحسين سرعة التشغيل:
1. فعل **Hardware Acceleration**
2. استخدم **WebView** محسن
3. قلل حجم الصور والموارد

## 🔐 الأمان

### حماية APK:
1. استخدم **Code Obfuscation**
2. فعل **App Signing** بمفتاح قوي
3. اختبر التطبيق على أجهزة مختلفة

### حماية البيانات:
1. استخدم **HTTPS** فقط
2. شفر البيانات الحساسة
3. تحقق من الأذونات المطلوبة

## 📱 اختبار التطبيق

### اختبار وظيفي:
- [ ] تسجيل الدخول والخروج
- [ ] إضافة وتعديل الطلبات
- [ ] التنقل بين الصفحات
- [ ] عمل الأزرار والقوائم

### اختبار الأداء:
- [ ] سرعة التحميل
- [ ] استهلاك البطارية
- [ ] استهلاك الذاكرة
- [ ] الاستجابة للمس

### اختبار التوافق:
- [ ] أجهزة مختلفة
- [ ] إصدارات أندرويد مختلفة
- [ ] أحجام شاشة مختلفة
- [ ] اتجاهات الشاشة

## 📋 قائمة مراجعة قبل النشر

- [ ] اختبار شامل للتطبيق
- [ ] تحديث رقم الإصدار
- [ ] إنشاء APK موقع
- [ ] اختبار APK على أجهزة مختلفة
- [ ] كتابة وصف التطبيق
- [ ] تحضير لقطات الشاشة
- [ ] مراجعة الأذونات المطلوبة
- [ ] التأكد من عمل جميع الميزات

## 🆘 الدعم والمساعدة

### الموارد المفيدة:
- [وثائق Capacitor](https://capacitorjs.com/docs)
- [وثائق Android](https://developer.android.com/docs)
- [مجتمع Capacitor](https://github.com/ionic-team/capacitor)

### الحصول على المساعدة:
1. تحقق من ملف `QUICK_START.md`
2. راجع ملف `README.md`
3. ابحث في Issues على GitHub
4. اسأل في مجتمع المطورين

---

**نصيحة:** احتفظ بنسخة احتياطية من مفاتيح التوقيع في مكان آمن!
