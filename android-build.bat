@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    🚀 Rihla Delivery - Android Build
echo ========================================
echo.

:: Check if Node.js is installed
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً.
    echo    تحميل من: https://nodejs.org
    pause
    exit /b 1
)

:: Check if npm is available
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح. يرجى التأكد من تثبيت Node.js بشكل صحيح.
    pause
    exit /b 1
)

:: Check if Java is installed
where java >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت. يرجى تثبيت Java JDK 8 أو أحدث.
    echo    تحميل من: https://adoptium.net
    pause
    exit /b 1
)

echo ✅ Node.js: 
node --version
echo ✅ npm: 
npm --version
echo ✅ Java: 
java -version 2>&1 | findstr "version"
echo.

echo 📋 خيارات البناء:
echo [1] بناء سريع (Build only)
echo [2] بناء وتشغيل (Build + Run)
echo [3] بناء APK للإنتاج (Release APK)
echo [4] تنظيف وإعادة بناء (Clean + Build)
echo [5] مزامنة Capacitor فقط (Sync only)
echo [6] فتح Android Studio
echo [0] خروج
echo.

set /p choice="اختر رقم الخيار: "

if "%choice%"=="1" goto build_only
if "%choice%"=="2" goto build_and_run
if "%choice%"=="3" goto build_release
if "%choice%"=="4" goto clean_build
if "%choice%"=="5" goto sync_only
if "%choice%"=="6" goto open_studio
if "%choice%"=="0" goto end
goto invalid_choice

:build_only
echo.
echo 🔨 بناء التطبيق...
echo.

echo 📦 تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل تثبيت التبعيات
    goto error_end
)

echo 🏗️ بناء مشروع Next.js...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل بناء مشروع Next.js
    goto error_end
)

echo 🔄 مزامنة Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo ❌ فشل مزامنة Capacitor
    goto error_end
)

echo ✅ تم بناء التطبيق بنجاح!
echo 💡 يمكنك الآن فتح Android Studio وتشغيل التطبيق
goto success_end

:build_and_run
echo.
echo 🔨 بناء وتشغيل التطبيق...
echo.

echo 📦 تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل تثبيت التبعيات
    goto error_end
)

echo 🏗️ بناء مشروع Next.js...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل بناء مشروع Next.js
    goto error_end
)

echo 🔄 مزامنة Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo ❌ فشل مزامنة Capacitor
    goto error_end
)

echo 🚀 فتح Android Studio...
call npx cap open android
if %errorlevel% neq 0 (
    echo ❌ فشل فتح Android Studio
    echo 💡 تأكد من تثبيت Android Studio
    goto error_end
)

echo ✅ تم فتح Android Studio بنجاح!
echo 💡 اضغط على زر Run في Android Studio لتشغيل التطبيق
goto success_end

:build_release
echo.
echo 📦 بناء APK للإنتاج...
echo.

echo 📦 تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل تثبيت التبعيات
    goto error_end
)

echo 🏗️ بناء مشروع Next.js للإنتاج...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل بناء مشروع Next.js
    goto error_end
)

echo 🔄 مزامنة Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo ❌ فشل مزامنة Capacitor
    goto error_end
)

echo 🏗️ بناء APK...
cd android
call gradlew assembleRelease
if %errorlevel% neq 0 (
    echo ❌ فشل بناء APK
    cd ..
    goto error_end
)
cd ..

echo ✅ تم بناء APK بنجاح!
echo 📁 مكان الملف: android\app\build\outputs\apk\release\app-release.apk
goto success_end

:clean_build
echo.
echo 🧹 تنظيف وإعادة بناء...
echo.

echo 🧹 تنظيف node_modules...
if exist node_modules rmdir /s /q node_modules

echo 🧹 تنظيف .next...
if exist .next rmdir /s /q .next

echo 🧹 تنظيف Android build...
cd android
call gradlew clean
cd ..

echo 📦 تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل تثبيت التبعيات
    goto error_end
)

echo 🏗️ بناء مشروع Next.js...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل بناء مشروع Next.js
    goto error_end
)

echo 🔄 مزامنة Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo ❌ فشل مزامنة Capacitor
    goto error_end
)

echo ✅ تم التنظيف وإعادة البناء بنجاح!
goto success_end

:sync_only
echo.
echo 🔄 مزامنة Capacitor...
echo.

call npx cap sync android
if %errorlevel% neq 0 (
    echo ❌ فشل مزامنة Capacitor
    goto error_end
)

echo ✅ تم مزامنة Capacitor بنجاح!
goto success_end

:open_studio
echo.
echo 🚀 فتح Android Studio...
echo.

call npx cap open android
if %errorlevel% neq 0 (
    echo ❌ فشل فتح Android Studio
    echo 💡 تأكد من تثبيت Android Studio
    goto error_end
)

echo ✅ تم فتح Android Studio بنجاح!
goto success_end

:invalid_choice
echo ❌ خيار غير صحيح. يرجى اختيار رقم من 0 إلى 6.
echo.
goto end

:error_end
echo.
echo ❌ حدث خطأ أثناء العملية!
echo.
echo 🔧 نصائح لحل المشاكل:
echo - تأكد من تثبيت Node.js و npm
echo - تأكد من تثبيت Java JDK 8+
echo - تأكد من تثبيت Android Studio
echo - تأكد من إعداد متغيرات البيئة ANDROID_HOME
echo - جرب تشغيل الأمر كمدير
echo.
pause
exit /b 1

:success_end
echo.
echo ✅ تمت العملية بنجاح!
echo.
echo 📱 بيانات الدخول للتطبيق:
echo 🔑 الحساب الافتراضي: default / 123456
echo 🔑 المدير العام: admin / password
echo 🔑 المشرف: manager / password
echo 🔑 المندوب: rep1 / password
echo 🔑 المحاسب: accountant1 / password
echo.
echo 📚 ملفات مفيدة:
echo - LOGIN_ACCOUNTS.md - جميع حسابات الدخول
echo - ANDROID_QUICK_START.md - دليل سريع للأندرويد
echo - README.md - الدليل الشامل
echo.

:end
pause
exit /b 0
