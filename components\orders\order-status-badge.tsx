import type { OrderStatus } from "@/types";

export function getStatusBadgeVariant(status: OrderStatus): "default" | "secondary" | "destructive" | "outline" {
  switch (status) {
    case 'في الانتظار':
      return 'outline';
    case 'مؤكد':
      return 'default';
    case 'قيد التوصيل':
      return 'secondary';
    case 'تم التسليم':
      return 'default';
    case 'راجع':
    case 'راجع عند المندوب':
    case 'ملغي':
      return 'destructive';
    case 'مؤجل':
    case 'تسليم جزئي':
    case 'تغيير سعر':
    case 'إعادة إرسال':
      return 'secondary';
    case 'في انتظار الإسناد':
      return 'outline';
    default:
      return 'outline';
  }
}
