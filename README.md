# Rihla Delivery App - تطبيق إدارة التوصيل

تطبيق شامل لإدارة التوصيل والطلبات يعمل على الويب والأندرويد والآيفون.

## المميزات

- 🌐 **متعدد المنصات**: يعمل على الويب والأندرويد والآيفون
- 📱 **تطبيق محمول أصلي**: باستخدام Capacitor
- 🔐 **نظام صلاحيات متقدم**: إدارة المستخدمين والصلاحيات
- 📦 **إدارة الطلبات**: إضافة وتعديل ومتابعة الطلبات
- 🚚 **إسناد التوصيل**: توزيع الطلبات على المندوبين
- 📊 **المحاسبة**: متابعة الحسابات والمدفوعات
- 📷 **مسح الباركود**: باستخدام كاميرا الجهاز
- 🔔 **الإشعارات**: تنبيهات فورية
- ⚙️ **الإعدادات المتقدمة**: إدارة الملف الشخصي وتفضيلات التطبيق
- 🗑️ **حذف مجمع للطلبات**: حذف الطلبات حسب معايير محددة
- 📅 **اختيار التواريخ**: واجهة سهلة لاختيار نطاقات التواريخ
- 🌙 **الوضع المظلم**: دعم كامل للثيم المظلم
- 🌍 **دعم العربية**: واجهة باللغة العربية مع دعم RTL

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **UI**: Tailwind CSS, shadcn/ui, Radix UI
- **Mobile**: Capacitor 5
- **Icons**: Lucide React
- **State Management**: React Context
- **Forms**: React Hook Form + Zod

## التثبيت والتشغيل

### متطلبات النظام

- Node.js 18+ 
- npm أو yarn أو pnpm

### تثبيت المشروع

```bash
# تحميل المشروع
git clone <repository-url>
cd rihla-delivery-app

# تثبيت المكتبات
npm install

# تشغيل التطبيق في وضع التطوير
npm run dev
```

### بناء التطبيق للويب

```bash
# بناء التطبيق للإنتاج
npm run build

# تشغيل النسخة المبنية
npm start
```

### بناء التطبيق للأندرويد

```bash
# إضافة منصة الأندرويد
npm run cap:add android

# بناء ومزامنة التطبيق
npm run cap:build

# فتح Android Studio
npm run android
```

### بناء التطبيق للآيفون

```bash
# إضافة منصة iOS
npm run cap:add ios

# بناء ومزامنة التطبيق
npm run cap:build

# فتح Xcode
npm run ios
```

## الحسابات التجريبية

- **مدير**: <EMAIL> / password
- **مشرف**: <EMAIL> / password  
- **موظف**: <EMAIL> / password

## هيكل المشروع

```
├── app/                    # صفحات Next.js
│   ├── page.tsx           # الصفحة الرئيسية
│   ├── orders/            # إدارة الطلبات
│   ├── settings/          # صفحة الإعدادات
│   ├── delete-orders/     # صفحة حذف الطلبات
│   ├── scanner/           # صفحة مسح الباركود
│   └── import-export/     # صفحة الاستيراد والتصدير
├── components/            # مكونات React
│   ├── ui/               # مكونات UI الأساسية (shadcn/ui)
│   ├── dashboard-page.tsx # صفحة لوحة التحكم
│   ├── login-page.tsx    # صفحة تسجيل الدخول
│   └── page-header.tsx   # مكون رأس الصفحة
├── contexts/             # React Contexts
│   └── auth-context.tsx  # سياق المصادقة
├── hooks/                # Custom Hooks
│   └── use-toast.ts      # هوك الإشعارات
├── lib/                  # مكتبات ووظائف مساعدة
│   ├── utils.ts          # وظائف مساعدة عامة
│   ├── permissions.ts    # نظام الصلاحيات
│   ├── order-data.ts     # بيانات الطلبات
│   └── capacitor.ts      # وظائف Capacitor
├── types/                # تعريفات الأنواع
├── public/               # ملفات عامة
├── capacitor.config.ts   # إعدادات Capacitor
├── next.config.js        # إعدادات Next.js
└── tailwind.config.js    # إعدادات Tailwind
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم، يرجى فتح issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.
